﻿using Core.Utilities.Paging;
using Core.Utilities.Results;
using Entities.Concrete;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Business.Abstract
{
    public interface IProductService
    {

        // Async yüzeyler
        Task<IDataResult<List<Product>>> GetAllAsync(CancellationToken ct = default);
        Task<IDataResult<PaginatedResult<Product>>> GetAllPaginatedAsync(ProductPagingParameters parameters, CancellationToken ct = default);
        Task<IDataResult<Product>> GetByIdAsync(int productId, CancellationToken ct = default);
        Task<IResult> AddAsync(Product product, CancellationToken ct = default);
        Task<IResult> UpdateAsync(Product product, CancellationToken ct = default);
        Task<IResult> DeleteAsync(int productId, CancellationToken ct = default);
    }
}
