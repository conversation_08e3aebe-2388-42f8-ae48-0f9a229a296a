﻿using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Business.ValidationRules.FluentValidation;
using Core.Aspects.Autofac.Validation;


using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Transaction;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace Business.Concrete
{
    public class MembershipFreezeHistoryManager : IMembershipFreezeHistoryService
    {
        private readonly IMembershipFreezeHistoryDal _membershipFreezeHistoryDal;

        public MembershipFreezeHistoryManager(IMembershipFreezeHistoryDal membershipFreezeHistoryDal)
        {
            _membershipFreezeHistoryDal = membershipFreezeHistoryDal;
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(1800)]
        public async Task<IDataResult<List<MembershipFreezeHistoryDto>>> GetAllAsync(CancellationToken ct = default)
        {
            var result = await _membershipFreezeHistoryDal.GetFreezeHistoryDetailsAsync(ct);
            return new SuccessDataResult<List<MembershipFreezeHistoryDto>>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(600)]
        public async Task<IDataResult<List<MembershipFreezeHistoryDto>>> GetByMembershipIdAsync(int membershipId, CancellationToken ct = default)
        {
            var result = await _membershipFreezeHistoryDal.GetFreezeHistoryByMembershipIdAsync(membershipId, ct);
            return new SuccessDataResult<List<MembershipFreezeHistoryDto>>(result);
        }

        [SecuredOperation("owner,admin")]
        [ValidationAspect(typeof(MembershipFreezeHistoryValidator))]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("MembershipFreezeHistory")]
        public async Task<IResult> AddAsync(MembershipFreezeHistory history, CancellationToken ct = default)
        {
            await _membershipFreezeHistoryDal.AddAsync(history, ct);
            return new SuccessResult();
        }

        [SecuredOperation("owner,admin")]
        [ValidationAspect(typeof(MembershipFreezeHistoryValidator))]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("MembershipFreezeHistory")]
        public async Task<IResult> UpdateAsync(MembershipFreezeHistory history, CancellationToken ct = default)
        {
            await _membershipFreezeHistoryDal.UpdateAsync(history, ct);
            return new SuccessResult();
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(300)]
        public async Task<IDataResult<int>> GetRemainingFreezeDaysAsync(int membershipId, CancellationToken ct = default)
        {
            var remainingDays = await _membershipFreezeHistoryDal.GetRemainingFreezeDaysWithCalculationAsync(membershipId, ct);
            return new SuccessDataResult<int>(remainingDays);
        }
    }
}