﻿using Core.Utilities.Results;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Business.Abstract
{
    public interface IMembershipFreezeHistoryService
    {
        // Async yüzeyler (sync kaldırıldı)
        Task<IDataResult<List<MembershipFreezeHistoryDto>>> GetAllAsync(CancellationToken ct = default);
        Task<IDataResult<List<MembershipFreezeHistoryDto>>> GetByMembershipIdAsync(int membershipId, CancellationToken ct = default);
        Task<IResult> AddAsync(MembershipFreezeHistory history, CancellationToken ct = default);
        Task<IResult> UpdateAsync(MembershipFreezeHistory history, CancellationToken ct = default);
        Task<IDataResult<int>> GetRemainingFreezeDaysAsync(int membershipId, CancellationToken ct = default);
    }

}
