﻿using Core.DataAccess;
using Core.Utilities.Results;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace DataAccess.Abstract
{
    public interface IRemainingDebtDal : IEntityRepository<RemainingDebt>
    {

        // ASYNC imzalar (CT'li)
        Task<List<RemainingDebtDetailDto>> GetRemainingDebtDetailsAsync(CancellationToken ct = default);
        Task<IResult> AddDebtPaymentWithBusinessLogicAsync(DebtPaymentDto debtPaymentDto, CancellationToken ct = default);
        Task<IResult> SoftDeleteRemainingDebtAsync(int remainingDebtId, int companyId, CancellationToken ct = default);
    }

}
