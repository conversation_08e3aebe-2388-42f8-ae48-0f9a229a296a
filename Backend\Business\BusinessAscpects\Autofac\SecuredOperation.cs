﻿using Business.Constants;
using Castle.DynamicProxy;
using Core.Utilities.Interceptors;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Core.Extensions;
using Core.Utilities.IoC;
using Core.Exceptions;
namespace Business.BusinessAscpects.Autofac
{
    public class SecuredOperation : MethodInterceptionAsync
    {
        private string[] _roles;
        private IHttpContextAccessor _httpContextAccessor;

        public SecuredOperation(string roles)
        {
            _roles = roles.Split(',');
            _httpContextAccessor = ServiceTool.ServiceProvider.GetService<IHttpContextAccessor>();
            Priority = 1; // En yüksek öncelik - Güvenlik kontrolü
        }

        protected override void OnBefore(IInvocation invocation)
        {
            // Kullanıcı kimliği doğrulanmamışsa (giriş yapmamışsa)
            if (!_httpContextAccessor.HttpContext.User.Identity.IsAuthenticated)
            {
                throw new UnauthorizedException("Oturum süresi dolmuş veya geçersiz. Lütfen tekrar giriş yapın.");
            }

            // Kullanıcı kimliği doğrulanmış ama gerekli rollere sahip değilse
            var roleClaims = _httpContextAccessor.HttpContext.User.ClaimRoles();
            foreach (var role in _roles)
            {
                if (roleClaims.Contains(role))
                {
                    return;
                }
            }
            throw new ForbiddenException(Messages.AuthorizationDenied);
        }
    }
}


