using Castle.DynamicProxy;
using Core.CrossCuttingConcerns.Caching;
using Core.Utilities.Interceptors;
using Core.Utilities.IoC;
using Core.Utilities.Security.CompanyContext;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Linq;
using System.Threading.Tasks;
using Core.CrossCuttingConcerns.Logging;

namespace Core.Aspects.Autofac.Caching
{
    /// <summary>
    /// Async akışlarda akıllı cache invalidation aspect'i
    /// Başarılı çağrı sonrası RemoveByPatternAsync ile temizler.
    /// </summary>
    public class SmartCacheRemoveAspectAsync : MethodInterceptionBaseAttribute
    {
        private readonly string _entityName;
        private readonly string _role;
        private readonly ICacheService _cacheService;
        private readonly ICompanyContext _companyContext;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly ILogService _logService;

        public SmartCacheRemoveAspectAsync(string entityName)
        {
            _entityName = entityName;
            _cacheService = ServiceTool.ServiceProvider.GetService<ICacheService>();
            _companyContext = ServiceTool.ServiceProvider.GetService<ICompanyContext>();
            _httpContextAccessor = ServiceTool.ServiceProvider.GetService<IHttpContextAccessor>();
            _logService = ServiceTool.ServiceProvider.GetService<ILogService>();
            Priority = 6; // En son - Cache invalidation
        }

        public SmartCacheRemoveAspectAsync(string role, bool isRoleBased)
        {
            if (isRoleBased)
            {
                _role = role;
            }
            else
            {
                _entityName = role;
            }

            _cacheService = ServiceTool.ServiceProvider.GetService<ICacheService>();
            _companyContext = ServiceTool.ServiceProvider.GetService<ICompanyContext>();
            _httpContextAccessor = ServiceTool.ServiceProvider.GetService<IHttpContextAccessor>();
            _logService = ServiceTool.ServiceProvider.GetService<ILogService>();
            Priority = 6; // En son - Cache invalidation
        }

        public override void Intercept(IInvocation invocation)
        {
            invocation.Proceed();

            var returnType = invocation.Method.ReturnType;
            if (returnType == typeof(Task))
            {
                var task = (Task)invocation.ReturnValue;
                invocation.ReturnValue = AfterAsync(task, invocation);
                return;
            }

            if (returnType.IsGenericType && returnType.GetGenericTypeDefinition() == typeof(Task<>))
            {
                var task = invocation.ReturnValue;
                var method = typeof(SmartCacheRemoveAspectAsync).GetMethod(nameof(AfterAsyncWithResult), System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                var genericMethod = method.MakeGenericMethod(returnType.GetGenericArguments()[0]);
                invocation.ReturnValue = genericMethod.Invoke(this, new object[] { task, invocation });
                return;
            }

            // Sync dönüş: başarıda sync invalidation yapma (mevcut sync aspect var). Burada no-op.
        }

        private async Task AfterAsync(Task task, IInvocation invocation)
        {
            await task.ConfigureAwait(false);
            // Task (void) dönüşlerde IResult bilgisi olmadığından başarıyı varsayılan olarak true kabul ediyoruz
            if (IsMethodSuccessful(null))
            {
                var companyId = GetCompanyId();
                if (companyId > 0)
                {
                    await InvalidateCacheAsync(companyId).ConfigureAwait(false);
                }
            }
        }

        private async Task<T> AfterAsyncWithResult<T>(Task<T> task, IInvocation invocation)
        {
            var result = await task.ConfigureAwait(false);
            // Task<T> dönüşlerde Success bilgisi varsa onu dikkate alalım
            if (IsMethodSuccessful(result))
            {
                var companyId = GetCompanyId();
                if (companyId > 0)
                {
                    await InvalidateCacheAsync(companyId).ConfigureAwait(false);
                }
            }
            return result;
        }

        private bool IsMethodSuccessful(object result)
        {
            if (result == null) return true;
            var type = result.GetType();
            var prop = type.GetProperty("Success");
            if (prop == null) return true;
            return (bool)prop.GetValue(result);
        }

        private int GetCompanyId()
        {
            try
            {
                var companyId = _companyContext.GetCompanyId();
                if (companyId > 0) return companyId;

                var companyIdClaim = _httpContextAccessor.HttpContext?.User?.Claims?
                    .FirstOrDefault(c => c.Type == "CompanyId");

                if (companyIdClaim != null && int.TryParse(companyIdClaim.Value, out int jwtCompanyId))
                {
                    return jwtCompanyId;
                }

                return -1;
            }
            catch
            {
                return -1;
            }
        }

        private async Task InvalidateCacheAsync(int companyId)
        {
            try
            {
                string[] patterns;

                if (!string.IsNullOrEmpty(_entityName))
                {
                    patterns = CacheInvalidationConfig.GetCachePatternsForEntity(_entityName, companyId);
                }
                else if (!string.IsNullOrEmpty(_role))
                {
                    patterns = CacheInvalidationConfig.GetCachePatternsForRole(_role, companyId);
                }
                else
                {
                    return;
                }

                foreach (var pattern in patterns)
                {
                    await _cacheService.RemoveByPatternAsync(pattern).ConfigureAwait(false);
                }
            }
            catch (Exception ex)
            {
                _logService?.Error($"Smart cache invalidation async error: {ex.Message}");
            }
        }
    }
}

