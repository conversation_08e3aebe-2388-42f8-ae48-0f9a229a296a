using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;

using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Transaction;
using Core.Utilities.Business;
using Core.Utilities.Results;
using Core.Utilities.Security.CompanyContext;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;


namespace Business.Concrete
{
    public class MemberWorkoutProgramManager : IMemberWorkoutProgramService
    {
        private readonly IMemberWorkoutProgramDal _memberWorkoutProgramDal;
        private readonly ICompanyContext _companyContext;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public MemberWorkoutProgramManager(
            IMemberWorkoutProgramDal memberWorkoutProgramDal,
            ICompanyContext companyContext,
            IHttpContextAccessor httpContextAccessor)
        {
            _memberWorkoutProgramDal = memberWorkoutProgramDal;
            _companyContext = companyContext;
            _httpContextAccessor = httpContextAccessor;
        }



        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("MemberWorkoutProgram")]
        public async Task<IResult> AssignProgramAsync(MemberWorkoutProgramAddDto assignmentDto, CancellationToken ct = default)
        {
            var companyId = _companyContext.GetCompanyId();
            return await _memberWorkoutProgramDal.AssignProgramWithValidationAsync(assignmentDto, companyId, ct);
        }



        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("MemberWorkoutProgram")]
        public async Task<IResult> UpdateAssignmentAsync(MemberWorkoutProgramUpdateDto assignmentDto, CancellationToken ct = default)
        {
            var companyId = _companyContext.GetCompanyId();
            return await _memberWorkoutProgramDal.UpdateAssignmentWithValidationAsync(assignmentDto, companyId, ct);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(1800)]
        public async Task<IDataResult<List<MemberWorkoutProgramListDto>>> GetCompanyAssignmentsAsync(CancellationToken ct = default)
        {
            var result = await _memberWorkoutProgramDal.GetCompanyAssignmentsAsync(ct);
            return new SuccessDataResult<List<MemberWorkoutProgramListDto>>(result);
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("MemberWorkoutProgram")]
        public async Task<IResult> DeleteAssignmentAsync(int assignmentId, CancellationToken ct = default)
        {
            var companyId = _companyContext.GetCompanyId();
            return await _memberWorkoutProgramDal.DeleteAssignmentWithValidationAsync(assignmentId, companyId, ct);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(600)]
        public async Task<IDataResult<List<MemberWorkoutProgramDto>>> GetMemberActiveProgramsAsync(int memberId, CancellationToken ct = default)
        {
            var ruleResult = await BusinessRules.RunAsync(
                CheckIfMemberBelongsToCompanyAsync(memberId, ct)
            );
            if (ruleResult != null)
            {
                return new ErrorDataResult<List<MemberWorkoutProgramDto>>(ruleResult.Message);
            }

            var result = await _memberWorkoutProgramDal.GetMemberActiveProgramsAsync(memberId, ct);
            return new SuccessDataResult<List<MemberWorkoutProgramDto>>(result);
        }





        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(600)]
        public async Task<IDataResult<List<MemberWorkoutProgramHistoryDto>>> GetMemberProgramHistoryAsync(int memberId, CancellationToken ct = default)
        {
            var ruleResult = await BusinessRules.RunAsync(
                CheckIfMemberBelongsToCompanyAsync(memberId, ct)
            );
            if (ruleResult != null)
            {
                return new ErrorDataResult<List<MemberWorkoutProgramHistoryDto>>(ruleResult.Message);
            }

            var result = await _memberWorkoutProgramDal.GetMemberProgramHistoryAsync(memberId, ct);
            return new SuccessDataResult<List<MemberWorkoutProgramHistoryDto>>(result);
        }





        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(3600)] // 1 saat cache - Üyenin program geçmişi (historical data)
        // Sync versiyon kaldırıldı

        // Mobil API için - User tablosu üzerinden erişim
        [SecuredOperation("member")]
        [PerformanceAspect(3)]
        [CacheAspect(300)] // 5 dakika cache - Mobile API aktif programlar
        // Sync versiyon kaldırıldı

        [SecuredOperation("member")]
        [PerformanceAspect(3)]
        [CacheAspect(300)]
        public async Task<IDataResult<List<MemberActiveWorkoutProgramDto>>> GetActiveWorkoutProgramsByUserIdAsync(int userId, CancellationToken ct = default)
        {
            var currentUserIdClaim = _httpContextAccessor.HttpContext?.User?.Claims?.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier");
            if (currentUserIdClaim == null || !int.TryParse(currentUserIdClaim.Value, out int currentUserId) || currentUserId != userId)
            {
                return new ErrorDataResult<List<MemberActiveWorkoutProgramDto>>("Yetkisiz erişim.");
            }

            var result = await _memberWorkoutProgramDal.GetActiveWorkoutProgramsByUserIdAsync(userId, ct);
            return new SuccessDataResult<List<MemberActiveWorkoutProgramDto>>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(600)]
        public async Task<IDataResult<int>> GetActiveAssignmentCountAsync(CancellationToken ct = default)
        {
            var companyId = _companyContext.GetCompanyId();
            var result = await _memberWorkoutProgramDal.GetActiveAssignmentCountAsync(companyId, ct);
            return new SuccessDataResult<int>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(1800)]
        public async Task<IDataResult<MemberWorkoutProgramDto>> GetAssignmentDetailAsync(int assignmentId, CancellationToken ct = default)
        {
            var result = await _memberWorkoutProgramDal.GetAssignmentDetailAsync(assignmentId, ct);
            if (result == null)
            {
                return new ErrorDataResult<MemberWorkoutProgramDto>("Program ataması bulunamadı.");
            }
            return new SuccessDataResult<MemberWorkoutProgramDto>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(1800)]
        public async Task<IDataResult<int>> GetAssignedMemberCountAsync(int workoutProgramTemplateId, CancellationToken ct = default)
        {
            var ruleResult = await BusinessRules.RunAsync(
                CheckIfProgramBelongsToCompanyAsync(workoutProgramTemplateId, ct)
            );
            if (ruleResult != null)
            {
                return new ErrorDataResult<int>(ruleResult.Message);
            }
            var result = await _memberWorkoutProgramDal.GetAssignedMemberCountAsync(workoutProgramTemplateId, ct);
            return new SuccessDataResult<int>(result);
        }

        [SecuredOperation("member")]
        [PerformanceAspect(3)]
        [CacheAspect(600)]
        public async Task<IDataResult<MemberWorkoutProgramDetailDto>> GetProgramDetailByUserAsync(int userId, int memberWorkoutProgramId, CancellationToken ct = default)
        {
            var currentUserIdClaim = _httpContextAccessor.HttpContext?.User?.Claims?.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier");
            if (currentUserIdClaim == null || !int.TryParse(currentUserIdClaim.Value, out int currentUserId) || currentUserId != userId)
            {
                return new ErrorDataResult<MemberWorkoutProgramDetailDto>("Yetkisiz erişim.");
            }

            var result = await _memberWorkoutProgramDal.GetProgramDetailByUserAsync(userId, memberWorkoutProgramId, ct);
            if (result == null)
            {
                return new ErrorDataResult<MemberWorkoutProgramDetailDto>(Messages.WorkoutProgramNotFound);
            }
            return new SuccessDataResult<MemberWorkoutProgramDetailDto>(result, Messages.WorkoutProgramDetailRetrieved);
        }

        // Sync GetAssignmentDetail kaldırıldı

        // Sync GetAssignedMemberCount kaldırıldı

        // Sync GetActiveAssignmentCount kaldırıldı

        // Sync GetProgramDetailByUser kaldırıldı

        // SOLID prensiplerine uygun: Business rule'lar DAL katmanına taşındı
        // Artık tüm validation logic'i DAL'da yapılıyor

        // Bu metotlar sadece Get operasyonlarında kullanıldığı için kaldırılmadı
        private async Task<IResult> CheckIfMemberBelongsToCompanyAsync(int memberId, CancellationToken ct)
        {
            var companyId = _companyContext.GetCompanyId();
            var member = await _memberWorkoutProgramDal.GetAsync(m => m.MemberID == memberId && m.CompanyID == companyId, ct);
            if (member == null)
            {
                return new ErrorResult("Üye bu şirkete ait değil.");
            }
            return new SuccessResult();
        }

        private async Task<IResult> CheckIfProgramBelongsToCompanyAsync(int workoutProgramTemplateId, CancellationToken ct)
        {
            var companyId = _companyContext.GetCompanyId();
            // Bu kontrol için basit bir sorgu yapıyoruz
            var assignment = await _memberWorkoutProgramDal.GetAsync(p => p.WorkoutProgramTemplateID == workoutProgramTemplateId && p.CompanyID == companyId, ct);
            if (assignment == null)
            {
                return new ErrorResult("Antrenman programı bu şirkete ait değil.");
            }
            return new SuccessResult();
        }
    }
}
