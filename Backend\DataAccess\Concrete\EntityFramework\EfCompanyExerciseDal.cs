using Core.DataAccess.EntityFramework;
using Core.Extensions;
using Core.Utilities.Paging;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using System.Threading;
using Microsoft.EntityFrameworkCore;
namespace DataAccess.Concrete.EntityFramework
{
    public class EfCompanyExerciseDal : EfCompanyEntityRepositoryBase<CompanyExercise, GymContext>, ICompanyExerciseDal
    {
        private readonly Core.Utilities.Security.CompanyContext.ICompanyContext _companyContext;

        // Constructor injection (Scalability için)
        public EfCompanyExerciseDal(Core.Utilities.Security.CompanyContext.ICompanyContext companyContext, GymContext context) : base(companyContext, context)
        {
            _companyContext = companyContext;
        }







        // Birleşik egzersiz listesi (System + Company)



        // SOLID prensiplerine uygun refactoring için eklenen metot
        public IResult UpdateCompanyExercise(int companyId, CompanyExerciseUpdateDto exerciseUpdateDto)
        {
            try
            {
                // DI kullanılıyor - Scalability optimized
                var existingExercise = _context.CompanyExercises
                    .FirstOrDefault(e => e.CompanyExerciseID == exerciseUpdateDto.CompanyExerciseID &&
                                       e.CompanyID == companyId);

                if (existingExercise == null)
                {
                    return new ErrorResult("Salon egzersizi bulunamadı.");
                }

                // Entity property'lerini güncelle
                existingExercise.ExerciseCategoryID = exerciseUpdateDto.ExerciseCategoryID;
                existingExercise.ExerciseName = exerciseUpdateDto.ExerciseName;
                existingExercise.Description = exerciseUpdateDto.Description;
                existingExercise.Instructions = exerciseUpdateDto.Instructions;
                existingExercise.MuscleGroups = exerciseUpdateDto.MuscleGroups;
                existingExercise.Equipment = exerciseUpdateDto.Equipment;
                existingExercise.DifficultyLevel = exerciseUpdateDto.DifficultyLevel;
                existingExercise.IsActive = exerciseUpdateDto.IsActive;
                existingExercise.UpdatedDate = DateTime.Now;

                _context.SaveChanges();

                return new SuccessResult("Salon egzersizi başarıyla güncellendi.");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Salon egzersizi güncellenirken hata oluştu: {ex.Message}");
            }
        }

        // Async versiyonlar
        public async Task<List<CompanyExerciseDto>> GetCompanyExercisesAsync(int companyId, CancellationToken ct = default)
        {
            var query = from ce in _context.CompanyExercises.AsNoTracking()
                        join ec in _context.ExerciseCategories.AsNoTracking() on ce.ExerciseCategoryID equals ec.ExerciseCategoryID
                        where ce.CompanyID == companyId && ce.IsActive == true && ec.IsActive == true
                        orderby ec.CategoryName, ce.ExerciseName
                        select new CompanyExerciseDto
                        {
                            CompanyExerciseID = ce.CompanyExerciseID,
                            CompanyID = ce.CompanyID,
                            ExerciseCategoryID = ce.ExerciseCategoryID,
                            CategoryName = ec.CategoryName,
                            ExerciseName = ce.ExerciseName,
                            Description = ce.Description,
                            Instructions = ce.Instructions,
                            MuscleGroups = ce.MuscleGroups,
                            Equipment = ce.Equipment,
                            DifficultyLevel = ce.DifficultyLevel,
                            DifficultyLevelText = ce.DifficultyLevel == 1 ? "Başlangıç" :
                                                 ce.DifficultyLevel == 2 ? "Orta" :
                                                 ce.DifficultyLevel == 3 ? "İleri" : "",
                            IsActive = ce.IsActive,
                            CreationDate = ce.CreationDate
                        };

            return await query.ToListAsync(ct);
        }

        public async Task<List<CompanyExerciseDto>> GetCompanyExercisesByCategoryAsync(int companyId, int categoryId, CancellationToken ct = default)
        {
            var query = from ce in _context.CompanyExercises.AsNoTracking()
                        join ec in _context.ExerciseCategories.AsNoTracking() on ce.ExerciseCategoryID equals ec.ExerciseCategoryID
                        where ce.CompanyID == companyId && ce.ExerciseCategoryID == categoryId &&
                              ce.IsActive == true && ec.IsActive == true
                        orderby ce.ExerciseName
                        select new CompanyExerciseDto
                        {
                            CompanyExerciseID = ce.CompanyExerciseID,
                            CompanyID = ce.CompanyID,
                            ExerciseCategoryID = ce.ExerciseCategoryID,
                            CategoryName = ec.CategoryName,
                            ExerciseName = ce.ExerciseName,
                            Description = ce.Description,
                            Instructions = ce.Instructions,
                            MuscleGroups = ce.MuscleGroups,
                            Equipment = ce.Equipment,
                            DifficultyLevel = ce.DifficultyLevel,
                            DifficultyLevelText = ce.DifficultyLevel == 1 ? "Başlangıç" :
                                                 ce.DifficultyLevel == 2 ? "Orta" :
                                                 ce.DifficultyLevel == 3 ? "İleri" : "",
                            IsActive = ce.IsActive,
                            CreationDate = ce.CreationDate
                        };

            return await query.ToListAsync(ct);
        }

        public async Task<PaginatedResult<CompanyExerciseDto>> GetCompanyExercisesFilteredAsync(int companyId, CompanyExerciseFilterDto filter, CancellationToken ct = default)
        {
            var query = from ce in _context.CompanyExercises.AsNoTracking()
                        join ec in _context.ExerciseCategories.AsNoTracking() on ce.ExerciseCategoryID equals ec.ExerciseCategoryID
                        where ce.CompanyID == companyId && ce.IsActive == true && ec.IsActive == true
                        select new CompanyExerciseDto
                        {
                            CompanyExerciseID = ce.CompanyExerciseID,
                            CompanyID = ce.CompanyID,
                            ExerciseCategoryID = ce.ExerciseCategoryID,
                            CategoryName = ec.CategoryName,
                            ExerciseName = ce.ExerciseName,
                            Description = ce.Description,
                            Instructions = ce.Instructions,
                            MuscleGroups = ce.MuscleGroups,
                            Equipment = ce.Equipment,
                            DifficultyLevel = ce.DifficultyLevel,
                            DifficultyLevelText = ce.DifficultyLevel == 1 ? "Başlangıç" :
                                                 ce.DifficultyLevel == 2 ? "Orta" :
                                                 ce.DifficultyLevel == 3 ? "İleri" : "",
                            IsActive = ce.IsActive,
                            CreationDate = ce.CreationDate
                        };

            if (filter.ExerciseCategoryID.HasValue)
            {
                var catId = filter.ExerciseCategoryID.Value;
                query = query.Where(x => x.ExerciseCategoryID == catId);
            }

            if (!string.IsNullOrWhiteSpace(filter.SearchTerm))
            {
                var searchTerm = filter.SearchTerm.ToLower();
                query = query.Where(x =>
                    (x.ExerciseName != null && x.ExerciseName.ToLower().Contains(searchTerm)) ||
                    (x.Description != null && x.Description.ToLower().Contains(searchTerm)) ||
                    (x.MuscleGroups != null && x.MuscleGroups.ToLower().Contains(searchTerm)));
            }

            if (filter.DifficultyLevel.HasValue)
            {
                var diff = filter.DifficultyLevel.Value;
                query = query.Where(x => x.DifficultyLevel == diff);
            }

            if (!string.IsNullOrWhiteSpace(filter.Equipment))
            {
                var equipment = filter.Equipment.ToLower();
                query = query.Where(x => x.Equipment != null && x.Equipment.ToLower().Contains(equipment));
            }

            query = query.OrderBy(x => x.CategoryName).ThenBy(x => x.ExerciseName);

            var totalCount = await query.CountAsync(ct);
            var items = await query
                .Skip((filter.Page - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .ToListAsync(ct);

            return new PaginatedResult<CompanyExerciseDto>(items, filter.Page, filter.PageSize, totalCount);
        }

        public async Task<List<CompanyExerciseDto>> SearchCompanyExercisesAsync(int companyId, string searchTerm, CancellationToken ct = default)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
            {
                return new List<CompanyExerciseDto>();
            }

            var query = from ce in _context.CompanyExercises.AsNoTracking()
                        join ec in _context.ExerciseCategories.AsNoTracking() on ce.ExerciseCategoryID equals ec.ExerciseCategoryID
                        where ce.CompanyID == companyId && ce.IsActive == true && ec.IsActive == true &&
                              (ce.ExerciseName.Contains(searchTerm) ||
                               (ce.Description != null && ce.Description.Contains(searchTerm)) ||
                               (ce.MuscleGroups != null && ce.MuscleGroups.Contains(searchTerm)) ||
                               ec.CategoryName.Contains(searchTerm))
                        orderby ce.ExerciseName
                        select new CompanyExerciseDto
                        {
                            CompanyExerciseID = ce.CompanyExerciseID,
                            CompanyID = ce.CompanyID,
                            ExerciseCategoryID = ce.ExerciseCategoryID,
                            CategoryName = ec.CategoryName,
                            ExerciseName = ce.ExerciseName,
                            Description = ce.Description,
                            Instructions = ce.Instructions,
                            MuscleGroups = ce.MuscleGroups,
                            Equipment = ce.Equipment,
                            DifficultyLevel = ce.DifficultyLevel,
                            DifficultyLevelText = ce.DifficultyLevel == 1 ? "Başlangıç" :
                                                 ce.DifficultyLevel == 2 ? "Orta" :
                                                 ce.DifficultyLevel == 3 ? "İleri" : "",
                            IsActive = ce.IsActive,
                            CreationDate = ce.CreationDate
                        };

            return await query.ToListAsync(ct);
        }

        public async Task<CompanyExerciseDto> GetCompanyExerciseDetailAsync(int companyId, int exerciseId, CancellationToken ct = default)
        {
            var query = from ce in _context.CompanyExercises.AsNoTracking()
                        join ec in _context.ExerciseCategories.AsNoTracking() on ce.ExerciseCategoryID equals ec.ExerciseCategoryID
                        where ce.CompanyID == companyId && ce.CompanyExerciseID == exerciseId && ce.IsActive == true
                        select new CompanyExerciseDto
                        {
                            CompanyExerciseID = ce.CompanyExerciseID,
                            CompanyID = ce.CompanyID,
                            ExerciseCategoryID = ce.ExerciseCategoryID,
                            CategoryName = ec.CategoryName,
                            ExerciseName = ce.ExerciseName,
                            Description = ce.Description,
                            Instructions = ce.Instructions,
                            MuscleGroups = ce.MuscleGroups,
                            Equipment = ce.Equipment,
                            DifficultyLevel = ce.DifficultyLevel,
                            DifficultyLevelText = ce.DifficultyLevel == 1 ? "Başlangıç" :
                                                 ce.DifficultyLevel == 2 ? "Orta" :
                                                 ce.DifficultyLevel == 3 ? "İleri" : "",
                            IsActive = ce.IsActive,
                            CreationDate = ce.CreationDate
                        };

            return await query.FirstOrDefaultAsync(ct);
        }

        public async Task<List<CombinedExerciseDto>> GetCombinedExercisesAsync(int companyId, CancellationToken ct = default)
        {
            var systemExercisesQuery = from se in _context.SystemExercises.AsNoTracking()
                                       join ec in _context.ExerciseCategories.AsNoTracking() on se.ExerciseCategoryID equals ec.ExerciseCategoryID
                                       where se.IsActive == true && ec.IsActive == true
                                       select new CombinedExerciseDto
                                       {
                                           ExerciseID = se.SystemExerciseID,
                                           ExerciseType = "System",
                                           ExerciseCategoryID = se.ExerciseCategoryID,
                                           CategoryName = ec.CategoryName,
                                           ExerciseName = se.ExerciseName,
                                           Description = se.Description,
                                           Instructions = se.Instructions,
                                           MuscleGroups = se.MuscleGroups,
                                           Equipment = se.Equipment,
                                           DifficultyLevel = se.DifficultyLevel,
                                           DifficultyLevelText = se.DifficultyLevel == 1 ? "Başlangıç" :
                                                                se.DifficultyLevel == 2 ? "Orta" :
                                                                se.DifficultyLevel == 3 ? "İleri" : "",
                                           IsActive = se.IsActive,
                                           CreationDate = se.CreationDate
                                       };

            var companyExercisesQuery = from ce in _context.CompanyExercises.AsNoTracking()
                                        join ec in _context.ExerciseCategories.AsNoTracking() on ce.ExerciseCategoryID equals ec.ExerciseCategoryID
                                        where ce.CompanyID == companyId && ce.IsActive == true && ec.IsActive == true
                                        select new CombinedExerciseDto
                                        {
                                            ExerciseID = ce.CompanyExerciseID,
                                            ExerciseType = "Company",
                                            ExerciseCategoryID = ce.ExerciseCategoryID,
                                            CategoryName = ec.CategoryName,
                                            ExerciseName = ce.ExerciseName,
                                            Description = ce.Description,
                                            Instructions = ce.Instructions,
                                            MuscleGroups = ce.MuscleGroups,
                                            Equipment = ce.Equipment,
                                            DifficultyLevel = ce.DifficultyLevel,
                                            DifficultyLevelText = ce.DifficultyLevel == 1 ? "Başlangıç" :
                                                                 ce.DifficultyLevel == 2 ? "Orta" :
                                                                 ce.DifficultyLevel == 3 ? "İleri" : "",
                                            IsActive = ce.IsActive,
                                            CreationDate = ce.CreationDate
                                        };

            var systemList = await systemExercisesQuery.ToListAsync(ct);
            var companyList = await companyExercisesQuery.ToListAsync(ct);

            var combined = systemList.Concat(companyList)
                                     .OrderBy(x => x.CategoryName)
                                     .ThenBy(x => x.ExerciseType)
                                     .ThenBy(x => x.ExerciseName)
                                     .ToList();

            return combined;
        }

        public async Task<List<CombinedExerciseDto>> GetCombinedExercisesByCategoryAsync(int companyId, int categoryId, CancellationToken ct = default)
        {
            var systemExercisesQuery = from se in _context.SystemExercises.AsNoTracking()
                                       join ec in _context.ExerciseCategories.AsNoTracking() on se.ExerciseCategoryID equals ec.ExerciseCategoryID
                                       where se.ExerciseCategoryID == categoryId && se.IsActive == true && ec.IsActive == true
                                       select new CombinedExerciseDto
                                       {
                                           ExerciseID = se.SystemExerciseID,
                                           ExerciseType = "System",
                                           ExerciseCategoryID = se.ExerciseCategoryID,
                                           CategoryName = ec.CategoryName,
                                           ExerciseName = se.ExerciseName,
                                           Description = se.Description,
                                           Instructions = se.Instructions,
                                           MuscleGroups = se.MuscleGroups,
                                           Equipment = se.Equipment,
                                           DifficultyLevel = se.DifficultyLevel,
                                           DifficultyLevelText = se.DifficultyLevel == 1 ? "Başlangıç" :
                                                                se.DifficultyLevel == 2 ? "Orta" :
                                                                se.DifficultyLevel == 3 ? "İleri" : "",
                                           IsActive = se.IsActive,
                                           CreationDate = se.CreationDate
                                       };

            var companyExercisesQuery = from ce in _context.CompanyExercises.AsNoTracking()
                                        join ec in _context.ExerciseCategories.AsNoTracking() on ce.ExerciseCategoryID equals ec.ExerciseCategoryID
                                        where ce.CompanyID == companyId && ce.ExerciseCategoryID == categoryId &&
                                              ce.IsActive == true && ec.IsActive == true
                                        select new CombinedExerciseDto
                                        {
                                            ExerciseID = ce.CompanyExerciseID,
                                            ExerciseType = "Company",
                                            ExerciseCategoryID = ce.ExerciseCategoryID,
                                            CategoryName = ec.CategoryName,
                                            ExerciseName = ce.ExerciseName,
                                            Description = ce.Description,
                                            Instructions = ce.Instructions,
                                            MuscleGroups = ce.MuscleGroups,
                                            Equipment = ce.Equipment,
                                            DifficultyLevel = ce.DifficultyLevel,
                                            DifficultyLevelText = ce.DifficultyLevel == 1 ? "Başlangıç" :
                                                                 ce.DifficultyLevel == 2 ? "Orta" :
                                                                 ce.DifficultyLevel == 3 ? "İleri" : "",
                                            IsActive = ce.IsActive,
                                            CreationDate = ce.CreationDate
                                        };

            var systemList = await systemExercisesQuery.ToListAsync(ct);
            var companyList = await companyExercisesQuery.ToListAsync(ct);

            var combined = systemList.Concat(companyList)
                                     .OrderBy(x => x.ExerciseType)
                                     .ThenBy(x => x.ExerciseName)
                                     .ToList();

            return combined;
        }

        public async Task<PaginatedResult<CombinedExerciseDto>> GetCombinedExercisesFilteredAsync(int companyId, SystemExerciseFilterDto filter, CancellationToken ct = default)
        {
            // N+1 optimizasyonu: Filtreleri veritabanı seviyesinde uygula
            var systemExercisesQuery = from se in _context.SystemExercises.AsNoTracking()
                                       join ec in _context.ExerciseCategories.AsNoTracking() on se.ExerciseCategoryID equals ec.ExerciseCategoryID
                                       where se.IsActive == true && ec.IsActive == true
                                       select new CombinedExerciseDto
                                       {
                                           ExerciseID = se.SystemExerciseID,
                                           ExerciseType = "System",
                                           ExerciseCategoryID = se.ExerciseCategoryID,
                                           CategoryName = ec.CategoryName,
                                           ExerciseName = se.ExerciseName,
                                           Description = se.Description,
                                           Instructions = se.Instructions,
                                           MuscleGroups = se.MuscleGroups,
                                           Equipment = se.Equipment,
                                           DifficultyLevel = se.DifficultyLevel,
                                           DifficultyLevelText = se.DifficultyLevel == 1 ? "Başlangıç" :
                                                                se.DifficultyLevel == 2 ? "Orta" :
                                                                se.DifficultyLevel == 3 ? "İleri" : "",
                                           IsActive = se.IsActive,
                                           CreationDate = se.CreationDate
                                       };

            var companyExercisesQuery = from ce in _context.CompanyExercises.AsNoTracking()
                                        join ec in _context.ExerciseCategories.AsNoTracking() on ce.ExerciseCategoryID equals ec.ExerciseCategoryID
                                        where ce.CompanyID == companyId && ce.IsActive == true && ec.IsActive == true
                                        select new CombinedExerciseDto
                                        {
                                            ExerciseID = ce.CompanyExerciseID,
                                            ExerciseType = "Company",
                                            ExerciseCategoryID = ce.ExerciseCategoryID,
                                            CategoryName = ec.CategoryName,
                                            ExerciseName = ce.ExerciseName,
                                            Description = ce.Description,
                                            Instructions = ce.Instructions,
                                            MuscleGroups = ce.MuscleGroups,
                                            Equipment = ce.Equipment,
                                            DifficultyLevel = ce.DifficultyLevel,
                                            DifficultyLevelText = ce.DifficultyLevel == 1 ? "Başlangıç" :
                                                                 ce.DifficultyLevel == 2 ? "Orta" :
                                                                 ce.DifficultyLevel == 3 ? "İleri" : "",
                                            IsActive = ce.IsActive,
                                            CreationDate = ce.CreationDate
                                        };

            // Filtreleri veritabanı seviyesinde uygula
            if (filter.ExerciseCategoryID.HasValue)
            {
                var catId = filter.ExerciseCategoryID.Value;
                systemExercisesQuery = systemExercisesQuery.Where(x => x.ExerciseCategoryID == catId);
                companyExercisesQuery = companyExercisesQuery.Where(x => x.ExerciseCategoryID == catId);
            }

            if (!string.IsNullOrWhiteSpace(filter.SearchTerm))
            {
                var searchTerm = filter.SearchTerm.ToLower();
                systemExercisesQuery = systemExercisesQuery.Where(x =>
                    (x.ExerciseName != null && x.ExerciseName.ToLower().Contains(searchTerm)) ||
                    (x.Description != null && x.Description.ToLower().Contains(searchTerm)) ||
                    (x.MuscleGroups != null && x.MuscleGroups.ToLower().Contains(searchTerm)));
                companyExercisesQuery = companyExercisesQuery.Where(x =>
                    (x.ExerciseName != null && x.ExerciseName.ToLower().Contains(searchTerm)) ||
                    (x.Description != null && x.Description.ToLower().Contains(searchTerm)) ||
                    (x.MuscleGroups != null && x.MuscleGroups.ToLower().Contains(searchTerm)));
            }

            if (filter.DifficultyLevel.HasValue)
            {
                var diff = filter.DifficultyLevel.Value;
                systemExercisesQuery = systemExercisesQuery.Where(x => x.DifficultyLevel == diff);
                companyExercisesQuery = companyExercisesQuery.Where(x => x.DifficultyLevel == diff);
            }

            if (!string.IsNullOrWhiteSpace(filter.Equipment))
            {
                var equipment = filter.Equipment.ToLower();
                systemExercisesQuery = systemExercisesQuery.Where(x => x.Equipment != null && x.Equipment.ToLower().Contains(equipment));
                companyExercisesQuery = companyExercisesQuery.Where(x => x.Equipment != null && x.Equipment.ToLower().Contains(equipment));
            }

            // ExerciseType filtresi - sadece ilgili sorguyu çalıştır
            IQueryable<CombinedExerciseDto> finalQuery;
            if (!string.IsNullOrWhiteSpace(filter.ExerciseType))
            {
                if (filter.ExerciseType == "System")
                {
                    finalQuery = systemExercisesQuery;
                }
                else if (filter.ExerciseType == "Company")
                {
                    finalQuery = companyExercisesQuery;
                }
                else
                {
                    finalQuery = systemExercisesQuery.Concat(companyExercisesQuery);
                }
            }
            else
            {
                finalQuery = systemExercisesQuery.Concat(companyExercisesQuery);
            }

            finalQuery = finalQuery.OrderBy(x => x.CategoryName)
                                   .ThenBy(x => x.ExerciseType)
                                   .ThenBy(x => x.ExerciseName);

            var totalCount = await finalQuery.CountAsync(ct);
            var items = await finalQuery
                .Skip((filter.Page - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .ToListAsync(ct);

            return new PaginatedResult<CombinedExerciseDto>(items, filter.Page, filter.PageSize, totalCount);
        }

        public async Task<IResult> UpdateCompanyExerciseAsync(int companyId, CompanyExerciseUpdateDto exerciseUpdateDto, CancellationToken ct = default)
        {
            try
            {
                var existingExercise = await _context.CompanyExercises
                    .FirstOrDefaultAsync(e => e.CompanyExerciseID == exerciseUpdateDto.CompanyExerciseID && e.CompanyID == companyId, ct);

                if (existingExercise == null)
                {
                    return new ErrorResult("Salon egzersizi bulunamadı.");
                }

                existingExercise.ExerciseCategoryID = exerciseUpdateDto.ExerciseCategoryID;
                existingExercise.ExerciseName = exerciseUpdateDto.ExerciseName;
                existingExercise.Description = exerciseUpdateDto.Description;
                existingExercise.Instructions = exerciseUpdateDto.Instructions;
                existingExercise.MuscleGroups = exerciseUpdateDto.MuscleGroups;
                existingExercise.Equipment = exerciseUpdateDto.Equipment;
                existingExercise.DifficultyLevel = exerciseUpdateDto.DifficultyLevel;
                existingExercise.IsActive = exerciseUpdateDto.IsActive;
                existingExercise.UpdatedDate = DateTime.Now;

                await _context.SaveChangesAsync(ct);

                return new SuccessResult("Salon egzersizi başarıyla güncellendi.");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Salon egzersizi güncellenirken hata oluştu: {ex.Message}");
            }
        }

        public async Task<IResult> AddCompanyExerciseAsync(CompanyExerciseAddDto exerciseAddDto, int companyId, CancellationToken ct = default)
        {
            try
            {
                var exercise = new CompanyExercise
                {
                    CompanyID = companyId,
                    ExerciseCategoryID = exerciseAddDto.ExerciseCategoryID,
                    ExerciseName = exerciseAddDto.ExerciseName,
                    Description = exerciseAddDto.Description,
                    Instructions = exerciseAddDto.Instructions,
                    MuscleGroups = exerciseAddDto.MuscleGroups,
                    Equipment = exerciseAddDto.Equipment,
                    DifficultyLevel = exerciseAddDto.DifficultyLevel,
                    IsActive = true,
                    CreationDate = DateTime.Now
                };

                await _context.CompanyExercises.AddAsync(exercise, ct);
                await _context.SaveChangesAsync(ct);

                return new SuccessResult("Salon egzersizi başarıyla eklendi.");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Salon egzersizi eklenirken hata oluştu: {ex.Message}");
            }
        }

        public async Task<IResult> SoftDeleteCompanyExerciseAsync(int exerciseId, int companyId, CancellationToken ct = default)
        {
            try
            {
                var exercise = await _context.CompanyExercises.FirstOrDefaultAsync(e =>
                    e.CompanyExerciseID == exerciseId && e.CompanyID == companyId, ct);

                if (exercise == null)
                {
                    return new ErrorResult("Salon egzersizi bulunamadı.");
                }

                exercise.IsActive = false;
                exercise.DeletedDate = DateTime.Now;
                await _context.SaveChangesAsync(ct);

                return new SuccessResult("Salon egzersizi başarıyla silindi.");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Salon egzersizi silinirken hata oluştu: {ex.Message}");
            }
        }


    }
}
