﻿using Core.Utilities.Paging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;

namespace Core.Extensions
{
    public static class IQueryableExtensions
    {
        public static async Task<PaginatedResult<T>> ToPaginatedResultAsync<T>(
            this IQueryable<T> source, int pageNumber, int pageSize, CancellationToken ct = default)
        {
            var totalCount = await source.CountAsync(ct).ConfigureAwait(false);
            var items = await source.Skip((pageNumber - 1) * pageSize).Take(pageSize).ToListAsync(ct).ConfigureAwait(false);
            return new PaginatedResult<T>(items, pageNumber, pageSize, totalCount);
        }
    }

}
