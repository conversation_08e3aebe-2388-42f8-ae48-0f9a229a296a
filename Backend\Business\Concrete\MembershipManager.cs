﻿using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Business.ValidationRules.FluentValidation;
using Core.Aspects.Autofac.Validation;


using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Transaction;
using Core.Utilities.Results;
using Core.Utilities.Security.CompanyContext;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Linq;
using System.Transactions;
using System.Threading;
using System.Threading.Tasks;

namespace Business.Concrete
{
    public class MembershipManager : IMembershipService
    {
        IMembershipDal _membershipDal;
        IPaymentDal _paymentDal;
        IRemainingDebtDal _remainingDebtDal;
        IMembershipFreezeHistoryService _freezeHistoryService;
        private readonly ICompanyContext _companyContext;

        public MembershipManager(IMembershipDal membershipDal,IPaymentDal paymentDal,IRemainingDebtDal remainingDebtDal, IMembershipFreezeHistoryService freezeHistoryService, ICompanyContext companyContext)
        {
            _membershipDal = membershipDal;
            _paymentDal = paymentDal;
            _remainingDebtDal = remainingDebtDal;
            _freezeHistoryService = freezeHistoryService;
            _companyContext = companyContext;
        }



        // ASYNC varyantlar
        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("Membership")]
        public async Task<IResult> CancelFreezeAsync(int membershipId, CancellationToken ct = default)
        {
            if (!await _membershipDal.IsMembershipFrozenAsync(membershipId, ct))
                return new ErrorResult("Üyelik dondurulmamış durumda.");

            var membership = await _membershipDal.GetAsync(m => m.MembershipID == membershipId, ct);
            if (membership == null)
                return new ErrorResult("Üyelik bulunamadı.");

            try
            {
                var freezeHistoriesResult = await _freezeHistoryService.GetByMembershipIdAsync(membershipId, ct);
                var lastFreezeHistory = freezeHistoriesResult?.Data?.OrderByDescending(x => x.CreationDate).FirstOrDefault();

                if (lastFreezeHistory != null)
                {
                    var history = new MembershipFreezeHistory
                    {
                        FreezeHistoryID = lastFreezeHistory.FreezeHistoryID,
                        MembershipID = membershipId,
                        CompanyID = membership.CompanyID,
                        StartDate = lastFreezeHistory.StartDate,
                        PlannedEndDate = lastFreezeHistory.PlannedEndDate,
                        ActualEndDate = DateTime.Now,
                        FreezeDays = lastFreezeHistory.FreezeDays,
                        UsedDays = 0,
                        CancellationType = "Tamamen İptal",
                        CreationDate = lastFreezeHistory.CreationDate
                    };

                    await _freezeHistoryService.UpdateAsync(history, ct);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"CancelFreezeAsync freeze history update skipped: {ex.Message}");
            }

            await _membershipDal.CancelFreezeAsync(membershipId, ct);
            return new SuccessResult("Üyelik dondurma işlemi tamamen iptal edildi.");
        }













        // Async varyant
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(1800)]
        public async Task<IDataResult<List<Membership>>> GetAllAsync(CancellationToken ct = default)
        {
            var list = await _membershipDal.GetAllAsync(ct: ct);
            list = list.OrderBy(x => x.MembershipID).ToList(); // deterministik sıralama
            return new SuccessDataResult<List<Membership>>(list);
        }





        // Async tam kapsam
        [SecuredOperation("owner,admin")]
        [ValidationAspect(typeof(MembershipAddValidator))]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("Membership")]
        public async Task<IResult> AddAsync(MembershipAddDto membershipDto, CancellationToken ct = default)
        {
            return await _membershipDal.AddMembershipWithPaymentAndDebtAsync(membershipDto, ct);
        }

        [SecuredOperation("owner,admin")]
        [ValidationAspect(typeof(MembershipUpdateValidator))]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("Membership")]
        public async Task<IResult> UpdateAsync(MembershipUpdateDto membershipDto, CancellationToken ct = default)
        {
            return await _membershipDal.UpdateMembershipWithDateManagementAsync(membershipDto, _companyContext.GetCompanyId(), ct);
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("Membership")]
        public async Task<IResult> DeleteAsync(int id, CancellationToken ct = default)
        {
            return await _membershipDal.DeleteMembershipWithRelatedDataAsync(id, _companyContext.GetCompanyId(), ct);
        }

        [SecuredOperation("owner,admin")]
        [CacheAspect(600)]
        public async Task<IDataResult<List<Membership>>> GetByMembershipIdAsync(int memberid, CancellationToken ct = default)
        {
            var list = await _membershipDal.GetAllAsync(c => c.MembershipID == memberid, ct);
            list = list.OrderBy(x => x.MembershipID).ToList();
            return new SuccessDataResult<List<Membership>>(list);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(300)]
        public async Task<IDataResult<LastMembershipInfoDto>> GetLastMembershipInfoAsync(int memberId, CancellationToken ct = default)
        {
            return await _membershipDal.GetLastMembershipInfoWithCalculationsAsync(memberId, _companyContext.GetCompanyId(), ct);
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("Membership")]
        public async Task<IResult> FreezeMembershipAsync(MembershipFreezeRequestDto freezeRequest, CancellationToken ct = default)
        {
            return await _membershipDal.FreezeMembershipWithValidationAsync(freezeRequest, int.MaxValue, ct);
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("Membership")]
        public async Task<IResult> UnfreezeMembershipAsync(int membershipId, CancellationToken ct = default)
        {
            if (!await _membershipDal.IsMembershipFrozenAsync(membershipId, ct))
                return new ErrorResult(Messages.MembershipAlreadyFrozen);
            await _membershipDal.UnfreezeMembershipAsync(membershipId, ct);
            return new SuccessResult(Messages.MembershipUnfrozen);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(600)]
        public async Task<IDataResult<List<MembershipFreezeDto>>> GetFrozenMembershipsAsync(CancellationToken ct = default)
        {
            var list = await _membershipDal.GetFrozenMembershipsAsync(ct);
            list = list.OrderBy(x => x.MembershipID).ToList();
            return new SuccessDataResult<List<MembershipFreezeDto>>(list);
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("Membership")]
        public async Task<IResult> ReactivateFromTodayAsync(int membershipId, CancellationToken ct = default)
        {
            if (!await _membershipDal.IsMembershipFrozenAsync(membershipId, ct))
                return new ErrorResult("Üyelik dondurulmamış durumda.");

            var membership = await _membershipDal.GetAsync(m => m.MembershipID == membershipId, ct);
            if (membership == null)
                return new ErrorResult("Üyelik bulunamadı.");

            var freezeHistoriesResult = await _freezeHistoryService.GetByMembershipIdAsync(membershipId, ct);
            var lastFreezeHistory = freezeHistoriesResult?.Data?.OrderByDescending(x => x.CreationDate).FirstOrDefault();

            if (lastFreezeHistory != null)
            {
                var usedDays = (int)(DateTime.Now - lastFreezeHistory.StartDate).TotalDays;
                var history = new MembershipFreezeHistory
                {
                    FreezeHistoryID = lastFreezeHistory.FreezeHistoryID,
                    MembershipID = membershipId,
                    CompanyID = membership.CompanyID,
                    StartDate = lastFreezeHistory.StartDate,
                    PlannedEndDate = lastFreezeHistory.PlannedEndDate,
                    ActualEndDate = DateTime.Now,
                    FreezeDays = lastFreezeHistory.FreezeDays,
                    UsedDays = usedDays,
                    CancellationType = "Erken Başlatma",
                    CreationDate = lastFreezeHistory.CreationDate
                };

                await _freezeHistoryService.UpdateAsync(history, ct);
            }

            await _membershipDal.ReactivateFromTodayAsync(membershipId, ct);
            return new SuccessResult("Üyelik bugünden itibaren aktif edildi.");
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(300)]
        public async Task<IDataResult<List<MembershipDetailForDeleteDto>>> GetMemberActiveMembershipsAsync(int memberId, CancellationToken ct = default)
        {
            var list = await _membershipDal.GetMemberActiveMembershipsAsync(memberId, ct);
            list = list.OrderBy(x => x.MembershipID).ToList();
            return new SuccessDataResult<List<MembershipDetailForDeleteDto>>(list);
        }
    }
}
