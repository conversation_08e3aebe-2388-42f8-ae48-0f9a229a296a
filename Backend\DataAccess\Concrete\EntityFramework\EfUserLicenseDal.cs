using Core.DataAccess.EntityFramework;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Transactions;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfUserLicenseDal : EfEntityRepositoryBase<UserLicense, GymContext>, IUserLicenseDal
    {
        // Constructor injection (Scalability i�in)
        public EfUserLicenseDal(GymContext context) : base(context) { }
        public async Task<List<UserLicenseDto>> GetUserLicenseDetailsAsync(CancellationToken ct = default)
        {
            var now = DateTime.Now;
            var query = from ul in _context.UserLicenses.AsNoTracking()
                        join u in _context.Users.AsNoTracking() on ul.UserID equals u.UserID
                        join lp in _context.LicensePackages.AsNoTracking() on ul.LicensePackageID equals lp.LicensePackageID
                        join uc in _context.UserCompanies.AsNoTracking() on u.UserID equals uc.UserID into ucGroup
                        from uc in ucGroup.DefaultIfEmpty()
                        join c in _context.Companies.AsNoTracking() on uc.CompanyId equals c.CompanyID into cGroup
                        from c in cGroup.DefaultIfEmpty()
                        where ul.IsActive == true && ul.EndDate >= now
                        select new UserLicenseDto
                        {
                            UserLicenseID = ul.UserLicenseID,
                            UserID = ul.UserID,
                            UserName = u.FirstName + " " + u.LastName,
                            UserEmail = u.Email,
                            CompanyName = c != null ? c.CompanyName : "Şirket Atanmamış",
                            LicensePackageID = ul.LicensePackageID,
                            PackageName = lp.Name,
                            Role = lp.Role,
                            StartDate = ul.StartDate,
                            EndDate = ul.EndDate,
                            RemainingDays = EF.Functions.DateDiffDay(now, ul.EndDate),
                            IsActive = ul.IsActive == true
                        };

            // Deterministik sıralama
            query = query.OrderByDescending(x => x.StartDate).ThenBy(x => x.UserLicenseID);
            return await query.ToListAsync(ct);
        }





        public async Task<List<UserLicenseDto>> GetActiveUserLicensesByUserIdAsync(int userId, CancellationToken ct = default)
        {
            var now = DateTime.Now;
            var query = from ul in _context.UserLicenses.AsNoTracking()
                        join u in _context.Users.AsNoTracking() on ul.UserID equals u.UserID
                        join lp in _context.LicensePackages.AsNoTracking() on ul.LicensePackageID equals lp.LicensePackageID
                        join uc in _context.UserCompanies.AsNoTracking() on u.UserID equals uc.UserID
                        join c in _context.Companies.AsNoTracking() on uc.CompanyId equals c.CompanyID
                        where ul.UserID == userId && ul.IsActive == true && ul.EndDate >= now
                        select new UserLicenseDto
                        {
                            UserLicenseID = ul.UserLicenseID,
                            UserID = ul.UserID,
                            UserName = u.FirstName + " " + u.LastName,
                            UserEmail = u.Email,
                            CompanyName = c.CompanyName,
                            LicensePackageID = ul.LicensePackageID,
                            PackageName = lp.Name,
                            Role = lp.Role,
                            StartDate = ul.StartDate,
                            EndDate = ul.EndDate,
                            RemainingDays = EF.Functions.DateDiffDay(now, ul.EndDate),
                            IsActive = ul.IsActive == true
                        };

            query = query.OrderByDescending(x => x.StartDate).ThenBy(x => x.UserLicenseID);
            return await query.ToListAsync(ct);
        }

        public async Task<UserLicenseDto> GetUserLicenseDetailAsync(int userLicenseId, CancellationToken ct = default)
        {
            var now = DateTime.Now;
            var query = from ul in _context.UserLicenses.AsNoTracking()
                        join u in _context.Users.AsNoTracking() on ul.UserID equals u.UserID
                        join lp in _context.LicensePackages.AsNoTracking() on ul.LicensePackageID equals lp.LicensePackageID
                        join uc in _context.UserCompanies.AsNoTracking() on u.UserID equals uc.UserID
                        join c in _context.Companies.AsNoTracking() on uc.CompanyId equals c.CompanyID
                        where ul.UserLicenseID == userLicenseId
                        select new UserLicenseDto
                        {
                            UserLicenseID = ul.UserLicenseID,
                            UserID = ul.UserID,
                            UserName = u.FirstName + " " + u.LastName,
                            UserEmail = u.Email,
                            CompanyName = c.CompanyName,
                            LicensePackageID = ul.LicensePackageID,
                            PackageName = lp.Name,
                            Role = lp.Role,
                            StartDate = ul.StartDate,
                            EndDate = ul.EndDate,
                            RemainingDays = EF.Functions.DateDiffDay(now, ul.EndDate),
                            IsActive = ul.IsActive == true
                        };

            return await query.FirstOrDefaultAsync(ct);
        }



        public async Task<PaginatedUserLicenseDto> GetUserLicenseDetailsPaginatedAsync(int page, int pageSize, string searchTerm, string sortBy, string companyName, int? remainingDaysMin, int? remainingDaysMax, CancellationToken ct = default)
        {
            var now = DateTime.Now;
            var query = from ul in _context.UserLicenses.AsNoTracking()
                        join u in _context.Users.AsNoTracking() on ul.UserID equals u.UserID
                        join lp in _context.LicensePackages.AsNoTracking() on ul.LicensePackageID equals lp.LicensePackageID
                        join uc in _context.UserCompanies.AsNoTracking() on u.UserID equals uc.UserID into ucGroup
                        from uc in ucGroup.DefaultIfEmpty()
                        join c in _context.Companies.AsNoTracking() on uc.CompanyId equals c.CompanyID into cGroup
                        from c in cGroup.DefaultIfEmpty()
                        where ul.IsActive == true && ul.EndDate >= now
                        select new UserLicenseDto
                        {
                            UserLicenseID = ul.UserLicenseID,
                            UserID = ul.UserID,
                            UserName = u.FirstName + " " + u.LastName,
                            UserEmail = u.Email,
                            CompanyName = c != null ? c.CompanyName : "Şirket Atanmamış",
                            LicensePackageID = ul.LicensePackageID,
                            PackageName = lp.Name,
                            Role = lp.Role,
                            StartDate = ul.StartDate,
                            EndDate = ul.EndDate,
                            RemainingDays = EF.Functions.DateDiffDay(now, ul.EndDate),
                            IsActive = ul.IsActive == true
                        };

            if (!string.IsNullOrEmpty(searchTerm))
            {
                query = query.Where(x => x.UserEmail.Contains(searchTerm) || x.CompanyName.Contains(searchTerm));
            }
            if (!string.IsNullOrEmpty(companyName))
            {
                query = query.Where(x => x.CompanyName.Contains(companyName));
            }
            if (remainingDaysMin.HasValue)
            {
                query = query.Where(x => x.RemainingDays >= remainingDaysMin.Value);
            }
            if (remainingDaysMax.HasValue)
            {
                query = query.Where(x => x.RemainingDays <= remainingDaysMax.Value);
            }

            switch (sortBy?.ToLower())
            {
                case "newest":
                    query = query.OrderByDescending(x => x.StartDate);
                    break;
                case "oldest":
                    query = query.OrderBy(x => x.StartDate);
                    break;
                case "expiring":
                    query = query.OrderBy(x => x.RemainingDays);
                    break;
                case "company":
                    query = query.OrderBy(x => x.CompanyName);
                    break;
                default:
                    query = query.OrderByDescending(x => x.StartDate);
                    break;
            }

            var totalCount = await query.CountAsync(ct);
            var totalPages = (int)Math.Ceiling((double)totalCount / pageSize);
            var data = await query.Skip((page - 1) * pageSize).Take(pageSize).ToListAsync(ct);

            return new PaginatedUserLicenseDto
            {
                Data = data,
                TotalCount = totalCount,
                PageNumber = page,
                PageSize = pageSize,
                TotalPages = totalPages,
                HasPreviousPage = page > 1,
                HasNextPage = page < totalPages
            };
        }

        public async Task<PaginatedUserLicenseDto> GetExpiredAndPassiveLicensesAsync(int page, int pageSize, string searchTerm, CancellationToken ct = default)
        {
            var now = DateTime.Now;
            var query = from ul in _context.UserLicenses.AsNoTracking()
                        join u in _context.Users.AsNoTracking() on ul.UserID equals u.UserID
                        join lp in _context.LicensePackages.AsNoTracking() on ul.LicensePackageID equals lp.LicensePackageID
                        join uc in _context.UserCompanies.AsNoTracking() on u.UserID equals uc.UserID into ucGroup
                        from uc in ucGroup.DefaultIfEmpty()
                        join c in _context.Companies.AsNoTracking() on uc.CompanyId equals c.CompanyID into cGroup
                        from c in cGroup.DefaultIfEmpty()
                        join cu in _context.CompanyUsers.AsNoTracking() on u.UserID equals cu.CompanyUserID into cuGroup
                        from cu in cuGroup.DefaultIfEmpty()
                        join city in _context.Cities.AsNoTracking() on cu.CityID equals city.CityID into cityGroup
                        from city in cityGroup.DefaultIfEmpty()
                        join town in _context.Towns.AsNoTracking() on cu.TownID equals town.TownID into townGroup
                        from town in townGroup.DefaultIfEmpty()
                        where !ul.IsActive == true || ul.EndDate < now
                        select new UserLicenseDto
                        {
                            UserLicenseID = ul.UserLicenseID,
                            UserID = ul.UserID,
                            UserName = u.FirstName + " " + u.LastName,
                            UserEmail = u.Email,
                            CompanyName = c != null ? c.CompanyName : "Şirket Atanmamış",
                            LicensePackageID = ul.LicensePackageID,
                            PackageName = lp.Name,
                            Role = lp.Role,
                            StartDate = ul.StartDate,
                            EndDate = ul.EndDate,
                            RemainingDays = EF.Functions.DateDiffDay(now, ul.EndDate),
                            IsActive = ul.IsActive == true
                        };

            if (!string.IsNullOrEmpty(searchTerm))
            {
                var searchLower = searchTerm.ToLower();
                query = query.Where(x =>
                    x.UserEmail.ToLower().Contains(searchLower) ||
                    x.CompanyName.ToLower().Contains(searchLower) ||
                    x.UserName.ToLower().Contains(searchLower) ||
                    x.PackageName.ToLower().Contains(searchLower) ||
                    x.Role.ToLower().Contains(searchLower)
                );
            }

            query = query.OrderByDescending(x => x.EndDate);

            var totalCount = await query.CountAsync(ct);
            var totalPages = (int)Math.Ceiling((double)totalCount / pageSize);
            var data = await query.Skip((page - 1) * pageSize).Take(pageSize).ToListAsync(ct);

            return new PaginatedUserLicenseDto
            {
                Data = data,
                TotalCount = totalCount,
                PageNumber = page,
                PageSize = pageSize,
                TotalPages = totalPages,
                HasPreviousPage = page > 1,
                HasNextPage = page < totalPages
            };
        }




        public async Task<IResult> PurchaseLicenseAsync(LicensePurchaseDto licensePurchaseDto, CancellationToken ct = default)
        {
            using (var scope = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled))
            {
                try
                {
                    var user = await _context.Users.AsNoTracking().FirstOrDefaultAsync(u => u.UserID == licensePurchaseDto.UserID, ct);
                    if (user == null)
                    {
                        return new ErrorResult("Kullanıcı bulunamadı");
                    }

                    var licensePackage = await _context.LicensePackages.AsNoTracking().FirstOrDefaultAsync(lp => lp.LicensePackageID == licensePurchaseDto.LicensePackageID, ct);
                    if (licensePackage == null)
                    {
                        return new ErrorResult("Lisans paketi bulunamadı");
                    }

                    var now = DateTime.Now;
                    var endDate = now.AddDays(licensePackage.DurationDays);

                    var existingLicenses = await _context.UserLicenses
                        .Where(ul => ul.UserID == licensePurchaseDto.UserID && ul.IsActive == true && ul.EndDate > now)
                        .ToListAsync(ct);

                    if (existingLicenses.Count > 0)
                    {
                        var existingPackageIds = existingLicenses.Select(x => x.LicensePackageID).Distinct().ToList();
                        var existingPackages = await _context.LicensePackages.AsNoTracking()
                            .Where(lp => existingPackageIds.Contains(lp.LicensePackageID))
                            .Select(lp => new { lp.LicensePackageID, lp.Role })
                            .ToDictionaryAsync(x => x.LicensePackageID, x => x.Role, ct);

                        foreach (var existingLicense in existingLicenses)
                        {
                            if (existingPackages.TryGetValue(existingLicense.LicensePackageID, out var existingRole)
                                && existingRole == licensePackage.Role)
                            {
                                var extensionDto = new LicenseExtensionByPackageDto
                                {
                                    UserLicenseId = existingLicense.UserLicenseID,
                                    LicensePackageId = licensePurchaseDto.LicensePackageID,
                                    PaymentMethod = licensePurchaseDto.PaymentMethod
                                };
                                return await ExtendLicenseByPackageAsync(extensionDto, ct);
                            }
                        }
                    }

                    var userLicense = new UserLicense
                    {
                        UserID = licensePurchaseDto.UserID,
                        LicensePackageID = licensePurchaseDto.LicensePackageID,
                        StartDate = now,
                        EndDate = endDate,
                        IsActive = true,
                        CreationDate = now
                    };
                    await _context.UserLicenses.AddAsync(userLicense, ct);
                    await _context.SaveChangesAsync(ct);

                    var transaction = new LicenseTransaction
                    {
                        UserID = licensePurchaseDto.UserID,
                        LicensePackageID = licensePurchaseDto.LicensePackageID,
                        UserLicenseID = userLicense.UserLicenseID,
                        Amount = licensePackage.Price,
                        PaymentMethod = licensePurchaseDto.PaymentMethod,
                        TransactionDate = now,
                        IsActive = true,
                        CreationDate = now
                    };
                    await _context.LicenseTransactions.AddAsync(transaction, ct);
                    await _context.SaveChangesAsync(ct);

                    scope.Complete();
                    return new SuccessResult("Lisans başarıyla satın alındı");
                }
                catch (Exception ex)
                {
                    return new ErrorResult($"Lisans satın alınırken bir hata oluştu: {ex.Message}");
                }
            }
        }

        public async Task<IResult> ExtendLicenseByPackageAsync(LicenseExtensionByPackageDto licenseExtensionByPackageDto, CancellationToken ct = default)
        {
            var userLicense = await _context.UserLicenses.FirstOrDefaultAsync(ul => ul.UserLicenseID == licenseExtensionByPackageDto.UserLicenseId, ct);
            if (userLicense == null)
            {
                return new ErrorResult("Lisans bulunamadı");
            }

            var licensePackage = await _context.LicensePackages.AsNoTracking().FirstOrDefaultAsync(lp => lp.LicensePackageID == licenseExtensionByPackageDto.LicensePackageId, ct);
            if (licensePackage == null)
            {
                return new ErrorResult("Lisans paketi bulunamadı");
            }

            if (string.IsNullOrWhiteSpace(licenseExtensionByPackageDto.PaymentMethod))
            {
                return new ErrorResult("Ödeme yöntemi seçilmelidir");
            }

            using (var scope = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled))
            {
                try
                {
                    var now = DateTime.Now;
                    userLicense.EndDate = userLicense.EndDate > now
                        ? userLicense.EndDate.AddDays(licensePackage.DurationDays)
                        : now.AddDays(licensePackage.DurationDays);
                    userLicense.IsActive = true;
                    userLicense.UpdatedDate = now;
                    _context.UserLicenses.Update(userLicense);
                    await _context.SaveChangesAsync(ct);

                    var transaction = new LicenseTransaction
                    {
                        UserID = userLicense.UserID,
                        LicensePackageID = licenseExtensionByPackageDto.LicensePackageId,
                        UserLicenseID = userLicense.UserLicenseID,
                        Amount = licensePackage.Price,
                        PaymentMethod = licenseExtensionByPackageDto.PaymentMethod,
                        TransactionDate = now,
                        IsActive = true,
                        CreationDate = now
                    };
                    await _context.LicenseTransactions.AddAsync(transaction, ct);
                    await _context.SaveChangesAsync(ct);

                    scope.Complete();
                    return new SuccessResult($"Lisans {licensePackage.Name} paketi ile başarıyla uzatıldı ({licensePackage.DurationDays} gün)");
                }
                catch (Exception ex)
                {
                    return new ErrorResult($"Lisans uzatılırken bir hata oluştu: {ex.Message}");
                }
            }
        }

        public async Task<List<string>> GetUserRolesAsync(int userId, CancellationToken ct = default)
        {
            var now = DateTime.Now;
            // N+1 optimizasyonu: Tek join sorgusu ile UserLicenses ve LicensePackages tablosunu birleştir
            var roles = await (from ul in _context.UserLicenses.AsNoTracking()
                              join lp in _context.LicensePackages.AsNoTracking() on ul.LicensePackageID equals lp.LicensePackageID
                              where ul.UserID == userId && ul.IsActive == true && ul.EndDate > now
                              select lp.Role)
                              .Distinct()
                              .ToListAsync(ct);

            return roles;
        }

        public async Task<IResult> RevokeLicenseWithValidationAsync(int userLicenseId, CancellationToken ct = default)
        {
            try
            {
                var userLicense = await _context.UserLicenses.FirstOrDefaultAsync(ul => ul.UserLicenseID == userLicenseId, ct);
                if (userLicense == null)
                {
                    return new ErrorResult("Lisans bulunamadı");
                }

                userLicense.IsActive = false;
                userLicense.EndDate = DateTime.Now;
                userLicense.UpdatedDate = DateTime.Now;

                await _context.SaveChangesAsync(ct);
                return new SuccessResult("Lisans başarıyla iptal edildi");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Lisans iptal edilirken hata oluştu: {ex.Message}");
            }
        }

        public async Task<IResult> AddUserLicenseWithDateManagementAsync(UserLicense userLicense, CancellationToken ct = default)
        {
            try
            {
                userLicense.CreationDate = DateTime.Now;
                userLicense.IsActive = true;
                await _context.UserLicenses.AddAsync(userLicense, ct);
                await _context.SaveChangesAsync(ct);
                return new SuccessResult("Kullanıcı lisansı başarıyla eklendi");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Kullanıcı lisansı eklenirken hata oluştu: {ex.Message}");
            }
        }

        public async Task<IResult> UpdateUserLicenseWithDateManagementAsync(UserLicense userLicense, CancellationToken ct = default)
        {
            try
            {
                userLicense.UpdatedDate = DateTime.Now;
                _context.UserLicenses.Update(userLicense);
                await _context.SaveChangesAsync(ct);
                return new SuccessResult("Kullanıcı lisansı başarıyla güncellendi");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Kullanıcı lisansı güncellenirken hata oluştu: {ex.Message}");
            }
        }

    }

}
