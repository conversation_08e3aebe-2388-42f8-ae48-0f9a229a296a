using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Transaction;
using Core.Aspects.Autofac.Validation;
using Core.Utilities.Paging;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using Business.ValidationRules.FluentValidation;

using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Threading;


namespace Business.Concrete
{
    public class CompanyExerciseManager : ICompanyExerciseService
    {
        ICompanyExerciseDal _companyExerciseDal;
        private readonly Core.Utilities.Security.CompanyContext.ICompanyContext _companyContext;

        public CompanyExerciseManager(ICompanyExerciseDal companyExerciseDal,
                                     Core.Utilities.Security.CompanyContext.ICompanyContext companyContext)
        {
            _companyExerciseDal = companyExerciseDal;
            _companyContext = companyContext;
        }


        // Async implementasyonlar
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(1800)]
        public async Task<IDataResult<List<CompanyExerciseDto>>> GetCompanyExercisesAsync(CancellationToken ct = default)
        {
            var companyId = _companyContext.GetCompanyId();
            var list = await _companyExerciseDal.GetCompanyExercisesAsync(companyId, ct);
            return new SuccessDataResult<List<CompanyExerciseDto>>(list);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(1800)]
        public async Task<IDataResult<List<CompanyExerciseDto>>> GetCompanyExercisesByCategoryAsync(int categoryId, CancellationToken ct = default)
        {
            var companyId = _companyContext.GetCompanyId();
            var list = await _companyExerciseDal.GetCompanyExercisesByCategoryAsync(companyId, categoryId, ct);
            return new SuccessDataResult<List<CompanyExerciseDto>>(list);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(1800)]
        public async Task<IDataResult<PaginatedResult<CompanyExerciseDto>>> GetCompanyExercisesFilteredAsync(CompanyExerciseFilterDto filter, CancellationToken ct = default)
        {
            var companyId = _companyContext.GetCompanyId();
            var result = await _companyExerciseDal.GetCompanyExercisesFilteredAsync(companyId, filter, ct);
            return new SuccessDataResult<PaginatedResult<CompanyExerciseDto>>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public async Task<IDataResult<List<CompanyExerciseDto>>> SearchCompanyExercisesAsync(string searchTerm, CancellationToken ct = default)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
            {
                return new ErrorDataResult<List<CompanyExerciseDto>>("Arama terimi boş olamaz.");
            }
            var companyId = _companyContext.GetCompanyId();
            var list = await _companyExerciseDal.SearchCompanyExercisesAsync(companyId, searchTerm, ct);
            return new SuccessDataResult<List<CompanyExerciseDto>>(list);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(1800)]
        public async Task<IDataResult<CompanyExerciseDto>> GetCompanyExerciseDetailAsync(int exerciseId, CancellationToken ct = default)
        {
            var companyId = _companyContext.GetCompanyId();
            var dto = await _companyExerciseDal.GetCompanyExerciseDetailAsync(companyId, exerciseId, ct);
            if (dto == null)
            {
                return new ErrorDataResult<CompanyExerciseDto>("Salon egzersizi bulunamadı.");
            }
            return new SuccessDataResult<CompanyExerciseDto>(dto);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(1800)]
        public async Task<IDataResult<CompanyExerciseDto>> GetByIdAsync(int exerciseId, CancellationToken ct = default)
        {
            var companyId = _companyContext.GetCompanyId();
            var dto = await _companyExerciseDal.GetCompanyExerciseDetailAsync(companyId, exerciseId, ct);
            if (dto == null)
            {
                return new ErrorDataResult<CompanyExerciseDto>("Salon egzersizi bulunamadı.");
            }
            return new SuccessDataResult<CompanyExerciseDto>(dto);
        }

        [SecuredOperation("owner,admin")]
        [ValidationAspect(typeof(CompanyExerciseAddValidator))]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("CompanyExercise")]
        public async Task<IResult> AddAsync(CompanyExerciseAddDto exerciseAddDto, CancellationToken ct = default)
        {
            var companyId = _companyContext.GetCompanyId();
            return await _companyExerciseDal.AddCompanyExerciseAsync(exerciseAddDto, companyId, ct);
        }

        [SecuredOperation("owner,admin")]
        [ValidationAspect(typeof(CompanyExerciseUpdateValidator))]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("CompanyExercise")]
        public async Task<IResult> UpdateAsync(CompanyExerciseUpdateDto exerciseUpdateDto, CancellationToken ct = default)
        {
            var companyId = _companyContext.GetCompanyId();
            return await _companyExerciseDal.UpdateCompanyExerciseAsync(companyId, exerciseUpdateDto, ct);
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("CompanyExercise")]
        public async Task<IResult> DeleteAsync(int exerciseId, CancellationToken ct = default)
        {
            var companyId = _companyContext.GetCompanyId();
            return await _companyExerciseDal.SoftDeleteCompanyExerciseAsync(exerciseId, companyId, ct);
        }

        // Birleşik async
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(1800)]
        public async Task<IDataResult<List<CombinedExerciseDto>>> GetCombinedExercisesAsync(CancellationToken ct = default)
        {
            var companyId = _companyContext.GetCompanyId();
            var list = await _companyExerciseDal.GetCombinedExercisesAsync(companyId, ct);
            return new SuccessDataResult<List<CombinedExerciseDto>>(list);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(1800)]
        public async Task<IDataResult<List<CombinedExerciseDto>>> GetCombinedExercisesByCategoryAsync(int categoryId, CancellationToken ct = default)
        {
            var companyId = _companyContext.GetCompanyId();
            var list = await _companyExerciseDal.GetCombinedExercisesByCategoryAsync(companyId, categoryId, ct);
            return new SuccessDataResult<List<CombinedExerciseDto>>(list);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(1800)]
        public async Task<IDataResult<PaginatedResult<CombinedExerciseDto>>> GetCombinedExercisesFilteredAsync(SystemExerciseFilterDto filter, CancellationToken ct = default)
        {
            var companyId = _companyContext.GetCompanyId();
            var result = await _companyExerciseDal.GetCombinedExercisesFilteredAsync(companyId, filter, ct);
            return new SuccessDataResult<PaginatedResult<CombinedExerciseDto>>(result);
        }

    }
}
