﻿using Core.Entities.Concrete;
using Core.Utilities.IoC;
using DataAccess.Abstract;
using DataAccess.Concrete.EntityFramework;
using FluentValidation;
using Microsoft.Extensions.DependencyInjection;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Business.ValidationRules.FluentValidation
{
    public class CompanyUserValidator : AbstractValidator<CompanyUser>
    {
        private readonly ICompanyUserDal _companyUserDal;

        public CompanyUserValidator()
        {
            // ServiceTool üzerinden servisleri al
            _companyUserDal = ServiceTool.ServiceProvider?.GetService<ICompanyUserDal>();

            RuleFor(p => p.Name).NotEmpty().WithMessage("İsim kısmı boş bırakılamaz.");
            RuleFor(p => p.PhoneNumber).NotEmpty().WithMessage("Telefon kısmı boş bırakılamaz.");
            RuleFor(p => p.PhoneNumber).MustAsync(async (user, phone, ct) => await BeUniquePhoneNumberAsync(user, ct))
                .WithMessage("Bu telefon numarası sistemde zaten kayıtlı.");
            RuleFor(p => p.PhoneNumber).Length(11).WithMessage("Telefon numarasını kontrol ediniz.");
            RuleFor(p => p.PhoneNumber).Must(StartsWithZero).WithMessage("Telefon numarası 0 ile başlamak zorundadır");
            RuleFor(x => x.Email).EmailAddress().WithMessage("E Posta adresini doğru giriniz.");
            RuleFor(x => x).MustAsync(BeUniqueEmailAsync).WithMessage("Bu e-posta adresi sistemde zaten kayıtlı.");
            RuleFor(p => p.CityID).NotEmpty().WithMessage("İl kısmı boş bırakılamaz.");
            RuleFor(p => p.TownID).NotEmpty().WithMessage("İlçe kısmı boş bırakılamaz.");
        }

        private async Task<bool> BeUniqueEmailAsync(CompanyUser user, CancellationToken ct)
        {
            // Null check for DI services - 1000+ salon sisteminde kritik
            if (_companyUserDal == null)
            {
                // Log the issue for monitoring in production
                System.Diagnostics.Debug.WriteLine("CompanyUserValidator: _companyUserDal is null - email validation bypassed");
                return true; // Validation geçer, başka katmanda kontrol edilir
            }

            // DI kullanılıyor - Scalability optimized
            if (user.CompanyUserID != 0)
            {
                var list = await _companyUserDal.GetAllAsync(u =>
                    u.Email == user.Email &&
                    u.CompanyUserID != user.CompanyUserID &&
                    u.IsActive == true, ct);
                return !list.Any();
            }
            else
            {
                var list = await _companyUserDal.GetAllAsync(u =>
                    u.Email == user.Email &&
                    u.IsActive == true, ct);
                return !list.Any();
            }
        }

        private async Task<bool> BeUniquePhoneNumberAsync(CompanyUser user, CancellationToken ct)
        {
            // Null check for DI services - 1000+ salon sisteminde kritik
            if (_companyUserDal == null)
            {
                // Log the issue for monitoring in production
                System.Diagnostics.Debug.WriteLine("CompanyUserValidator: _companyUserDal is null - phone validation bypassed");
                return true; // Validation geçer, başka katmanda kontrol edilir
            }

            // DI kullanılıyor - Scalability optimized
            if (user.CompanyUserID != 0)
            {
                var list = await _companyUserDal.GetAllAsync(u =>
                    u.PhoneNumber == user.PhoneNumber &&
                    u.CompanyUserID != user.CompanyUserID &&
                    u.IsActive == true, ct);
                return !list.Any();
            }
            else
            {
                var list = await _companyUserDal.GetAllAsync(u =>
                    u.PhoneNumber == user.PhoneNumber &&
                    u.IsActive == true, ct);
                return !list.Any();
            }
        }

        private bool StartsWithZero(string arg)
        {
            return arg.StartsWith("0");
        }
    }
}