using Entities.DTOs;
using FluentValidation;

namespace Business.ValidationRules.FluentValidation
{
    public class MembershipAddValidator : AbstractValidator<MembershipAddDto>
    {
        public MembershipAddValidator()
        {
            RuleFor(x => x.MemberID)
                .GreaterThan(0).WithMessage("Geçerli bir üye seçimi gereklidir.");

            RuleFor(x => x.MembershipTypeID)
                .GreaterThan(0).WithMessage("Geçerli bir üyelik türü seçimi gereklidir.");

            RuleFor(x => x.StartDate)
                .NotEmpty().WithMessage("Başlangıç tarihi boş bırakılamaz.");

            RuleFor(x => x.EndDate)
                .NotEmpty().WithMessage("Bitiş tarihi boş bırakılamaz.")
                .GreaterThan(x => x.StartDate).WithMessage("Bitiş tarihi başlangıç tarihinden sonra olmalıdır.");

            RuleFor(x => x.Price)
                .GreaterThanOrEqualTo(0).WithMessage("Fiyat negatif olamaz.");

            RuleFor(x => x.PaymentMethod)
                .NotEmpty().WithMessage("Ödeme yöntemi boş bırakılamaz.");
        }
    }

    public class MembershipUpdateValidator : AbstractValidator<MembershipUpdateDto>
    {
        public MembershipUpdateValidator()
        {
            RuleFor(x => x.MembershipID)
                .GreaterThan(0).WithMessage("Geçerli bir üyelik ID'si gereklidir.");

            RuleFor(x => x.MembershipTypeID)
                .GreaterThan(0).WithMessage("Geçerli bir üyelik türü seçimi gereklidir.");

            RuleFor(x => x.StartDate)
                .NotEmpty().WithMessage("Başlangıç tarihi boş bırakılamaz.");

            RuleFor(x => x.EndDate)
                .NotEmpty().WithMessage("Bitiş tarihi boş bırakılamaz.")
                .GreaterThan(x => x.StartDate).WithMessage("Bitiş tarihi başlangıç tarihinden sonra olmalıdır.");
        }
    }
}

