SQL Injection ve XSS Risk Taraması — Plan (AI Prompt - TXT)
Amaç

Kod tabanında SQL Injection ve XSS risk kalıplarının tespiti ve davranışı değiştirmeyen minimal düzeltmeler ile giderilmesi
Çok dosyalı yapıda kararlı, adım adım ve güvenli ilerleme
Kapsam

Backend (.NET/EF Core): WebAPI/Controllers, Business, DataAccess (EF) ve DbContext
Frontend (Angular): Bileşen şablonları (HTML), bileşen sınıfları (TS), doğrudan DOM manipülasyonları
SSR/Node (varsa): XSS açısından şablon üretim yüzeyi
İnceleme Sırası (A’dan Z’ye — tamamlama temelli)

Backend
WebAPI/Controllers (tüm controller dosyaları)
DataAccess/Concrete/EntityFramework/DbContext (ör. GymContext.cs)
EF DAL dosyaları:
EfCityDal.cs
EfCompanyAdressDal.cs
EfCompanyDal.cs
EfCompanyExerciseDal.cs
EfCompanyUserDal.cs
EfDebtPaymentDal.cs
EfEntryExitHistoryDal.cs
EfExerciseCategoryDal.cs
EfExpenseDal.cs
EfLicensePackageDal.cs
EfLicenseTransactionDal.cs
EfMemberDal.cs
EfMemberWorkoutProgramDal.cs
EfMembershipDal.cs
EfMembershipFreezeHistoryDal.cs
EfMembershipTypeDal.cs
EfOperationClaimDal.cs
EfPaymentDal.cs
EfProductDal.cs
EfRemainingDebtDal.cs
EfSystemExerciseDal.cs
EfTownDal.cs
EfTransactionDal.cs
EfUnifiedCompanyDal.cs
EfUserCompanyDal.cs
EfUserDal.cs
EfUserDeviceDal.cs
EfUserLicenseDal.cs
EfUserOperationClaimDal.cs
EfWorkoutProgramExerciseDal.cs
EfWorkoutProgramTemplateDal.cs
Frontend
src/app/components/** altındaki tüm .html ve .ts dosyaları
src/app/shared, src/app/pipes, src/app/services (DOM’a HTML enjektesi, sanitizer bypass noktaları)
SSR (varsa): server tarafı şablon üretim akışı
İnceleme Döngüsü (her dosya/şablon için)

Public metot ve şablon taraması
SQL Injection kalıpları gözlemi
XSS kalıpları gözlemi
Risk bulunması halinde minimal, davranışı koruyan çözüm üretimi
Dosya bazlı kısa özetin kayıt altına alınması
Değerlendirme Ölçütleri — SQL Injection

Raw SQL kullanımı: ExecuteSqlRaw/FromSqlRaw/SqlQuery vb. metin temelli çağrılar
String birleştirme/format ile sorgu inşası
Interpolated string ($"...") ile ham SQL
Dinamik ORDER BY/sütun adlarının metinsel eklenmesi
Dış girdilerin (query/body/route) doğrudan SQL’e dahil edilmesi
Stored procedure çağrılarında eksik parametreleme
Tercih Seti — SQL Injection

EF LINQ sorguları (varsayılan tercih)
Parametreli/Interpolated ham SQL (ExecuteSqlInterpolated/FromSqlInterpolated) veya SqlParameter ile ExecuteSqlRaw
Çoklu ekleme/güncelleme: entity bazlı AddRange/UpdateRange + SaveChanges (transaction), yüksek hacimde TVP/SqlBulkCopy gibi yöntemler
Dinamik sıralama/kolon adı: allow-list ve sabit eşleme
Değerlendirme Ölçütleri — XSS

[innerHTML] binding kullanımı
ElementRef.nativeElement.innerHTML atamaları
Renderer2.setProperty(..., 'innerHTML', ...)
DomSanitizer.bypassSecurityTrustHtml/Url/Script/Style/ResourceUrl çağrıları
enableHtml ile gelen HTML’in gösterimi
[href]/[src]/[style] attribute binding’lerinde dış girdi kullanımı
SSR’de dış girdinin kaçışsız şablona gömülmesi
Tercih Seti — XSS

Varsayılan interpolasyon ({{ ... }}) ile kaçışlı çıktı
HTML gerekliliğinde güvenli bileşenle parçalama; zorunlu durumlarda sanitize edilmiş ve allow-list’li yaklaşım
Attribute binding’lerinde güvenli tip/allow-list; javascript:/data: gibi vektörlerin dışlanması
Doğrudan innerHTML set’lerinin kaldırılması lehine güvenli bağlama
Rapor Formatı (dosya başına)

Dosya: <yol/dosyaAdı>
İncelenen yüzey: <metotlar/şablonlar>
Bulgu(lar):
Tür: SQLi | XSS
Örüntü: <kısa açıklama>
Şiddet: Düşük | Orta | Yüksek
Çözüm özeti:
Uygulanan desen: <ör. parametreli SQL, EF LINQ, güvenli bağlama>
Kapsam: Minimal | Orta
Davranış: Korundu
Notlar: <performans/tenant/SOLID>
Sonraki:
Kısıtlar ve İlkeler

SOLID ve katmanlı mimarinin korunumu
Multi-tenant izolasyon (CompanyID) kurallarının korunumu
Performans bilinci: gereksiz round-trip, N+1, büyük dize birleştirmelerinden kaçınma
Toplu/regex ile düzenleme yok; tek tek, sınırlı ve kontrollü değişiklik
Paket ekleme/kaldırma yok; mevcut bağımlıklarla çözüm
Domain davranışının korunumu; yalnızca güvenlik odaklı minimal değişiklik
Doğrulama

Birim/integrasyon testlerinin çalıştırılması ve temel işlevlerin korunumu
Kritik uçlarda hızlı duman testi
Değişiklik seti ve gerekçe listesi ile kayıt altına alma
Bitirme Kriterleri

Backend Controller ve tüm EF DAL dosyaları: SQL Injection yüzeyinin taranması ve gerekli düzeltmelerin tamamlanması
Frontend bileşenleri: XSS yüzeyinin taranması ve gerekli düzeltmelerin tamamlanması
Dosya bazlı raporlar ve genel özetin hazır olması