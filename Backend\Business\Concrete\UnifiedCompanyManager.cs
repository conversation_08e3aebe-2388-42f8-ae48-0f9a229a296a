using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Transaction;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Business.Concrete
{
    public class UnifiedCompanyManager : IUnifiedCompanyService
    {
        private readonly IUnifiedCompanyDal _unifiedCompanyDal;

        public UnifiedCompanyManager(IUnifiedCompanyDal unifiedCompanyDal)
        {
            _unifiedCompanyDal = unifiedCompanyDal;
        }

        // ASYNC varyant
        [SecuredOperation("owner")]
        [LogAspect]
        [PerformanceAspect(5)]
        public async Task<IResult> AddUnifiedCompanyAsync(UnifiedCompanyAddDto unifiedCompanyDto, CancellationToken ct = default)
        {
            var result = await _unifiedCompanyDal.AddUnifiedCompanyWithAllEntitiesAsync(unifiedCompanyDto, ct);
            if (result.Success)
            {
                return new SuccessResult(result.Message);
            }
            return new ErrorResult(result.Message);
        }
    }
}
