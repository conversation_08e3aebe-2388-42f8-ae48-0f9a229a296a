using System.Collections.Generic;

namespace Core.CrossCuttingConcerns.Caching
{
    /// <summary>
    /// Cache invalidation kurallarını merkezi olarak yöneten configuration sınıfı
    /// Entity bazlı cache dependency mapping'i
    /// </summary>
    public static class CacheInvalidationConfig
    {
        /// <summary>
        /// Entity güncellendiğinde hangi cache pattern'lerinin temizleneceğini belirler
        /// Key: Entity adı, Value: Temizlenecek cache pattern'leri
        /// </summary>
        public static readonly Dictionary<string, string[]> EntityCacheDependencies = new()
        {
            // Member entity güncellendiğinde temizlenecek cache'ler
            ["Member"] = new[]
            {
                "imemberservice:*",           // Gerçek cache key pattern
                "imembershipservice:*",       // İlişkili entity
                "ipaymentservice:*",          // Ödeme bilgileri
                "itransactionservice:*",      // İşlem geçmişi
                "iremainingdebtservice:*",    // Bor<PERSON> durumu
                "imemberworkoutprogramservice:*" // Antrenman programları
            },

            // Membership entity güncellendiğinde temizlenecek cache'ler
            ["Membership"] = new[]
            {
                "imembershipservice:*",
                "imemberservice:*",           // Üye bilgileri etkilenir
                "ipaymentservice:*",          // Ödeme durumu değişir
                "itransactionservice:*",
                "iremainingdebtservice:*"
            },

            // Payment entity güncellendiğinde temizlenecek cache'ler
            ["Payment"] = new[]
            {
                "ipaymentservice:*",
                "itransactionservice:*",
                "iremainingdebtservice:*",
                "imembershipservice:*",       // Üyelik durumu etkilenebilir
                "imemberservice:*"            // Üye finansal durumu
            },

            // Product entity güncellendiğinde temizlenecek cache'ler
            ["Product"] = new[]
            {
                "iproductservice:*",
                "itransactionservice:*",      // Ürün satış işlemleri
                "ipaymentservice:*"           // Ürün ödemeleri
            },

            // WorkoutProgram entity güncellendiğinde temizlenecek cache'ler
            ["WorkoutProgram"] = new[]
            {
                "iworkoutprogramservice:*",
                "imemberworkoutprogramservice:*",
                "iworkoutprogramtemplateservice:*"
            },

            // User entity güncellendiğinde temizlenecek cache'ler
            // ⚠️ CRITICAL: GetUserCompanyId cache'ini temizleme
            ["User"] = new[]
            {
                "iuserservice:*",
                "imemberservice:*",
                "icompanyuserservice:*",
                "iusercompanyservice:*"       // GetUserCompanyId için kritik!
            },

            // CompanyAdress entity güncellendiğinde temizlenecek cache'ler
            ["CompanyAdress"] = new[]
            {
                "icompanyadressservice:*",
                "icompanyservice:*"
            },

            // CompanyExercise entity güncellendiğinde temizlenecek cache'ler
            ["CompanyExercise"] = new[]
            {
                "icompanyexerciseservice:*",
                "icombinedexerciseservice:*",
                "iworkoutprogramtemplateservice:*",
                "imemberworkoutprogramservice:*"
            },

            // Company entity güncellendiğinde temizlenecek cache'ler
            ["Company"] = new[]
            {
                "icompanyservice:*",
                "icompanyadressservice:*",
                "icompanyuserservice:*",
                "iusercompanyservice:*"
            },

            // CompanyUser entity güncellendiğinde temizlenecek cache'ler
            ["CompanyUser"] = new[]
            {
                "icompanyuserservice:*",
                "icompanyservice:*",
                "iuserservice:*",
                "iusercompanyservice:*"
            },

            // DebtPayment entity güncellendiğinde temizlenecek cache'ler
            ["DebtPayment"] = new[]
            {
                "idebtpaymentservice:*",
                "ipaymentservice:*",
                "iremainingdebtservice:*",
                "imemberservice:*"            // Üye borç durumu değişir
            },

            // ExerciseCategory entity güncellendiğinde temizlenecek cache'ler
            ["ExerciseCategory"] = new[]
            {
                "iexercisecategoryservice:*",
                "isystemexerciseservice:*",
                "icompanyexerciseservice:*",
                "icombinedexerciseservice:*"
            },

            // Expense entity güncellendiğinde temizlenecek cache'ler
            ["Expense"] = new[]
            {
                "iexpenseservice:*"
            },

            // LicensePackage entity güncellendiğinde temizlenecek cache'ler
            ["LicensePackage"] = new[]
            {
                "ilicensepackageservice:*",
                "iuserlicenseservice:*",
                "ilicensetransactionservice:*"
            },

            // LicenseTransaction entity güncellendiğinde temizlenecek cache'ler
            ["LicenseTransaction"] = new[]
            {
                "ilicensetransactionservice:*",
                "iuserlicenseservice:*"
            },

            // MembershipFreezeHistory entity güncellendiğinde temizlenecek cache'ler
            ["MembershipFreezeHistory"] = new[]
            {
                "imembershipfreezehistoryservice:*",
                "imembershipservice:*"
            },

            // MembershipType entity güncellendiğinde temizlenecek cache'ler
            ["MembershipType"] = new[]
            {
                "imembershiptypeservice:*",
                "imembershipservice:*"
            },

            // MemberWorkoutProgram entity güncellendiğinde temizlenecek cache'ler
            ["MemberWorkoutProgram"] = new[]
            {
                "imemberworkoutprogramservice:*"
            },

            // OperationClaim entity güncellendiğinde temizlenecek cache'ler
            ["OperationClaim"] = new[]
            {
                "ioperationclaimservice:*",
                "iuseroperationclaimservice:*"
            },

            // UserOperationClaim entity güncellendiğinde temizlenecek cache'ler
            ["UserOperationClaim"] = new[]
            {
                "iuseroperationclaimservice:*"
            },

            // WorkoutProgramTemplate entity güncellendiğinde temizlenecek cache'ler
            ["WorkoutProgramTemplate"] = new[]
            {
                "iworkoutprogramtemplateservice:*",
                "imemberworkoutprogramservice:*"
            },

            // RemainingDebt entity güncellendiğinde temizlenecek cache'ler
            ["RemainingDebt"] = new[]
            {
                "iremainingdebtservice:*",
                "ipaymentservice:*",
                "idebtpaymentservice:*",
                "imemberservice:*"
            },

            // SystemExercise entity güncellendiğinde temizlenecek cache'ler
            ["SystemExercise"] = new[]
            {
                "isystemexerciseservice:*",
                "icombinedexerciseservice:*"
            },

            // Transaction entity güncellendiğinde temizlenecek cache'ler
            ["Transaction"] = new[]
            {
                "itransactionservice:*",
                "imemberservice:*",
                "ipaymentservice:*"           // Transaction durumu payment raporlarını etkiler
            },

            // UserCompany entity güncellendiğinde temizlenecek cache'ler
            ["UserCompany"] = new[]
            {
                "iusercompanyservice:*",      // Kendi cache'leri
                "iuserservice:*"              // ⚠️ CRITICAL: GetUserCompanyId cache'ini temizle!
            }
        };

        /// <summary>
        /// Role bazlı cache invalidation kuralları
        /// Key: Role adı, Value: Temizlenecek cache pattern'leri
        /// </summary>
        public static readonly Dictionary<string, string[]> RoleCacheDependencies = new()
        {
            // Member rolü için cache invalidation
            ["member"] = new[]
            {
                "imemberservice:*",
                "imembershipservice:*",
                "ipaymentservice:*",
                "itransactionservice:*",
                "iremainingdebtservice:*",
                "imemberworkoutprogramservice:*"
            },

            // Admin rolü için cache invalidation (daha geniş)
            ["admin"] = new[]
            {
                "imemberservice:*",
                "imembershipservice:*",
                "ipaymentservice:*",
                "itransactionservice:*",
                "iremainingdebtservice:*",
                "imemberworkoutprogramservice:*",
                "iproductservice:*",
                "iworkoutprogramservice:*",
                "icompanyservice:*"
            },

            // Owner rolü için cache invalidation (admin + sistem yönetimi)
            ["owner"] = new[]
            {
                "imemberservice:*",
                "imembershipservice:*",
                "ipaymentservice:*",
                "itransactionservice:*",
                "iremainingdebtservice:*",
                "imemberworkoutprogramservice:*",
                "iproductservice:*",
                "iworkoutprogramservice:*",
                "icompanyservice:*",
                "ioperationclaimservice:*",
                "iuseroperationclaimservice:*",
                "iuserlicenseservice:*",
                "isystemexerciseservice:*",
                "icompanyuserservice:*",
                "iusercompanyservice:*",
                "ilicensetransactionservice:*"
            }
        };

        /// <summary>
        /// Belirli bir entity için cache pattern'lerini döndürür
        /// </summary>
        /// <param name="entityName">Entity adı</param>
        /// <param name="companyId">Company ID</param>
        /// <returns>Temizlenecek cache pattern'leri</returns>
        public static string[] GetCachePatternsForEntity(string entityName, int companyId)
        {
            if (EntityCacheDependencies.TryGetValue(entityName, out var patterns))
            {
                var result = new string[patterns.Length];
                for (int i = 0; i < patterns.Length; i++)
                {
                    result[i] = $"gym:{companyId}:{patterns[i]}";
                }
                return result;
            }

            return new string[0];
        }

        /// <summary>
        /// Belirli bir role için cache pattern'lerini döndürür
        /// </summary>
        /// <param name="role">Role adı</param>
        /// <param name="companyId">Company ID</param>
        /// <returns>Temizlenecek cache pattern'leri</returns>
        public static string[] GetCachePatternsForRole(string role, int companyId)
        {
            if (RoleCacheDependencies.TryGetValue(role.ToLower(), out var patterns))
            {
                var result = new string[patterns.Length];
                for (int i = 0; i < patterns.Length; i++)
                {
                    result[i] = $"gym:{companyId}:{patterns[i]}";
                }
                return result;
            }

            return new string[0];
        }
    }
}
