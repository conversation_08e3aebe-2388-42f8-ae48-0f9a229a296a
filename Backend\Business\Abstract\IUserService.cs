﻿using Core.Entities.Concrete;
using Core.Utilities.Results;
using Entities.Concrete;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Business.Abstract
{
    public interface IUserService
    {
        // SY<PERSON> y<PERSON> (kaldırıldı - tüm operasyonlar async)


        // ASYNC yü<PERSON>yler (eklenir)
        Task<IDataResult<List<User>>> GetAllAsync(CancellationToken ct = default);
        Task<IResult> AddAsync(User user, CancellationToken ct = default);
        Task<IResult> UpdateAsync(User user, CancellationToken ct = default);
        Task<IResult> UpdatePasswordAsync(int targetUserId, byte[] passwordHash, byte[] passwordSalt, bool requirePasswordChange, CancellationToken ct = default);
        Task<IResult> DeleteAsync(int id, CancellationToken ct = default);
        Task<List<OperationClaim>> GetClaimsAsync(User user, CancellationToken ct = default);
        Task<User> GetByMailAsync(string email, CancellationToken ct = default);
        Task<IDataResult<User>> GetByIdAsync(int userId, CancellationToken ct = default);
        Task<IDataResult<List<User>>> GetNonMembersAsync(CancellationToken ct = default);
        Task<IDataResult<List<User>>> GetNonMembersPaginatedAsync(int page, int pageSize, string searchTerm, CancellationToken ct = default);
        Task<IDataResult<int>> GetNonMembersCountAsync(string searchTerm, CancellationToken ct = default);
    }
}
