﻿using Core.Utilities.Results;
using Entities.Concrete;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Threading;


namespace Business.Abstract
{
    public interface ILicensePackageService
    {

        // Async y<PERSON><PERSON>yler (CT ile)
        Task<IDataResult<List<LicensePackage>>> GetAllAsync(CancellationToken ct = default);
        Task<IDataResult<LicensePackage>> GetByIdAsync(int id, CancellationToken ct = default);
        Task<IResult> AddAsync(LicensePackage licensePackage, CancellationToken ct = default);
        Task<IResult> UpdateAsync(LicensePackage licensePackage, CancellationToken ct = default);
        Task<IResult> DeleteAsync(int id, CancellationToken ct = default);
    }
}
