﻿using Core.Entities.Concrete;
using Core.Utilities.Results;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Business.Abstract
{
    public interface ICompanyUserService
    {

        // Async imzalar (CT zorunlu)
        Task<IDataResult<List<CompanyUser>>> GetAllAsync(CancellationToken ct = default);
        Task<IResult> AddAsync(CompanyUser companyUser, CancellationToken ct = default);
        Task<IResult> UpdateAsync(CompanyUser companyUser, CancellationToken ct = default);
        Task<IResult> DeleteAsync(int id, CancellationToken ct = default);
        Task<IDataResult<List<CompanyUser>>> GetByCityIdAsync(int cityId, CancellationToken ct = default);
        Task<IDataResult<List<CompanyDetailDto>>> GetCompanyDetailsAsync(CancellationToken ct = default);
        Task<IDataResult<List<CompanyDetailDto>>> GetCompanyUserDetailsByCityIdAsync(int cityId, CancellationToken ct = default);
        Task<IDataResult<List<CompanyUserDetailDto>>> GetCompanyUserDetailsAsync(CancellationToken ct = default);
        Task<IDataResult<CompanyUser>> GetByIdAsync(int companyUserID, CancellationToken ct = default);
        Task<IDataResult<CompanyUserFullDetailDto>> GetCompanyUserFullDetailsAsync(int companyUserID, CancellationToken ct = default);
        Task<IResult> UpdateCompanyUserFullAsync(CompanyUserFullUpdateDto updateDto, CancellationToken ct = default);
        Task<IDataResult<PaginatedCompanyUserDto>> GetCompanyUsersPaginatedAsync(int pageNumber, int pageSize, string searchTerm = "", CancellationToken ct = default);
        Task<IResult> SoftDeleteCompanyUserAsync(int companyUserID, CancellationToken ct = default);
        Task<IDataResult<List<DeletedCompanyUserDto>>> GetDeletedCompanyUsersAsync(CancellationToken ct = default);
        Task<IResult> RestoreCompanyUserAsync(int companyUserID, CancellationToken ct = default);
    }
}
