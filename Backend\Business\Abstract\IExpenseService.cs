using Core.Utilities.Results;
using Core.Utilities.Paging;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Threading;

using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Abstract
{
    public interface IExpenseService
    {

        // Async yüzeyler (CT ile) — sync kalır
        Task<IDataResult<List<Expense>>> GetAllAsync(CancellationToken ct = default);
        Task<IDataResult<Expense>> GetByIdAsync(int expenseId, CancellationToken ct = default);
        Task<IResult> AddAsync(Expense expense, CancellationToken ct = default);
        Task<IResult> UpdateAsync(Expense expense, CancellationToken ct = default);
        Task<IResult> DeleteAsync(int expenseId, CancellationToken ct = default);

        Task<IDataResult<List<ExpenseDto>>> GetExpensesByDateRangeAsync(DateTime startDate, DateTime endDate, CancellationToken ct = default);
        Task<IDataResult<ExpenseDashboardDto>> GetExpenseDashboardDataAsync(int year, int month, CancellationToken ct = default);
        Task<IDataResult<MonthlyExpenseDto>> GetMonthlyExpenseAsync(int year, CancellationToken ct = default);
        Task<IDataResult<ExpenseTotals>> GetExpenseTotalsAsync(ExpensePagingParameters parameters, CancellationToken ct = default);
        Task<IDataResult<PaginatedResult<ExpenseDto>>> GetExpensesPaginatedAsync(ExpensePagingParameters parameters, CancellationToken ct = default);
        Task<IDataResult<List<ExpenseDto>>> GetAllExpensesFilteredAsync(ExpensePagingParameters parameters, CancellationToken ct = default);
    }
}