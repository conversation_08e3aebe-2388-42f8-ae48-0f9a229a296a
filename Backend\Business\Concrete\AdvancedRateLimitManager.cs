using Business.Abstract;
using Core.Utilities.Results;
using Business.BusinessAscpects;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using System.Security.Cryptography;
using System.Text;
using Core.CrossCuttingConcerns.Caching;
using System.Threading;
using System.Threading.Tasks;

namespace Business.Concrete
{
    public class AdvancedRateLimitManager : IAdvancedRateLimitService
    {
        private readonly ICacheService _cacheService;

        // Login Rate Limiting Constants
        private const int MAX_LOGIN_ATTEMPTS = 5;
        private readonly int[] LOGIN_BAN_DURATIONS = { 5, 15, 60, 360, 1440 }; // dakika: 5dk, 15dk, 1saat, 6saat, 24saat

        // Register Rate Limiting Constants
        private const int MAX_REGISTER_ATTEMPTS = 3;
        private readonly int[] REGISTER_BAN_DURATIONS = { 60, 360, 1440, 10080 }; // dakika: 1saat, 6saat, 24saat, 7gün

        // Profile Image Upload Rate Limiting Constants
        private const int MAX_PROFILE_IMAGE_UPLOADS_PER_DAY = 3;

        // File Download Rate Limiting Constants
        private const int MAX_FILE_DOWNLOADS_PER_10_MINUTES = 5;

        public AdvancedRateLimitManager(ICacheService cacheService)
        {
            _cacheService = cacheService;
        }

        [LogAspect]
        [PerformanceAspect(3)]
        public async Task<IResult> CheckLoginAttemptAsync(string ipAddress, string deviceFingerprint, CancellationToken ct = default)
        {
            ct.ThrowIfCancellationRequested();
            // Sadece device fingerprint tabanlı ban kontrolü
            // IP tabanlı ban kullanmıyoruz çünkü paylaşılan IP'lerde (spor salonu WiFi vb.)
            // diğer kullanıcıları da etkileyebilir
            var banKey = GetLoginBanKey(ipAddress, deviceFingerprint);

            var cachedBanEndTime = await _cacheService.GetAsync<DateTime?>(banKey);
            if (cachedBanEndTime.HasValue)
            {
                var remaining = cachedBanEndTime.Value - DateTime.Now;

                if (remaining.TotalMinutes > 0)
                {
                    var remainingMinutes = Math.Ceiling(remaining.TotalMinutes);
                    return new ErrorResult($"Çok fazla başarısız giriş denemesi. Lütfen {remainingMinutes} dakika sonra tekrar deneyin.");
                }
                else
                {
                    // Ban süresi dolmuş, cache'den temizle
                    await _cacheService.RemoveAsync(banKey);
                }
            }

            return new SuccessResult();
        }

        [LogAspect]
        [PerformanceAspect(3)]
        public async Task<IResult> RecordFailedLoginAsync(string ipAddress, string deviceFingerprint, CancellationToken ct = default)
        {
            ct.ThrowIfCancellationRequested();
            var failKey = GetLoginFailKey(ipAddress, deviceFingerprint);
            var banKey = GetLoginBanKey(ipAddress, deviceFingerprint);
            var violationKey = GetLoginViolationKey(ipAddress, deviceFingerprint);

            int failCount = await _cacheService.GetAsync<int>(failKey);
            failCount++;

            if (failCount >= MAX_LOGIN_ATTEMPTS)
            {
                // Violation sayısını al
                int violationCount = await _cacheService.GetAsync<int>(violationKey);

                // Progressive ban süresi hesapla
                int banDuration = GetProgressiveBanDuration(violationCount, LOGIN_BAN_DURATIONS);
                var banEndTime = DateTime.Now.AddMinutes(banDuration);

                // Sadece device fingerprint tabanlı ban uygula
                await _cacheService.SetAsync(banKey, banEndTime, TimeSpan.FromMinutes(banDuration));

                // Violation sayısını artır
                violationCount++;
                await _cacheService.SetAsync(violationKey, violationCount, TimeSpan.FromMinutes(10080)); // 7 gün boyunca violation sayısını tut

                // Fail count'u sıfırla
                await _cacheService.RemoveAsync(failKey);

                return new ErrorResult($"Çok fazla başarısız giriş denemesi. {banDuration} dakika boyunca giriş yapamazsınız.");
            }
            else
            {
                // Fail count'u güncelle (5 dakika boyunca tut)
                await _cacheService.SetAsync(failKey, failCount, TimeSpan.FromMinutes(5));
            }

            return new SuccessResult();
        }

        [LogAspect]
        [PerformanceAspect(3)]
        public async Task<IResult> RecordSuccessfulLoginAsync(string ipAddress, string deviceFingerprint, CancellationToken ct = default)
        {
            ct.ThrowIfCancellationRequested();
            var failKey = GetLoginFailKey(ipAddress, deviceFingerprint);
            var banKey = GetLoginBanKey(ipAddress, deviceFingerprint);

            // Başarılı giriş sonrası sadece device fingerprint tabanlı fail count'u ve ban'ı sıfırla
            await _cacheService.RemoveAsync(failKey);
            await _cacheService.RemoveAsync(banKey);

            return new SuccessResult();
        }

        [LogAspect]
        [PerformanceAspect(3)]
        public async Task<IDataResult<int>> GetRemainingLoginBanTimeAsync(string ipAddress, string deviceFingerprint, CancellationToken ct = default)
        {
            ct.ThrowIfCancellationRequested();
            var banKey = GetLoginBanKey(ipAddress, deviceFingerprint);

            var cachedBanEndTime = await _cacheService.GetAsync<DateTime?>(banKey);
            if (cachedBanEndTime.HasValue)
            {
                var remaining = cachedBanEndTime.Value - DateTime.Now;

                if (remaining.TotalMinutes > 0)
                {
                    return new SuccessDataResult<int>((int)Math.Ceiling(remaining.TotalMinutes));
                }
                else
                {
                    // Ban süresi dolmuş, cache'den temizle
                    await _cacheService.RemoveAsync(banKey);
                }
            }

            return new SuccessDataResult<int>(0);
        }

        [LogAspect]
        [PerformanceAspect(3)]
        public async Task<IResult> CheckRegisterAttemptAsync(string ipAddress, CancellationToken ct = default)
        {
            ct.ThrowIfCancellationRequested();
            var banKey = GetRegisterBanKey(ipAddress);

            var cachedBanEndTime = await _cacheService.GetAsync<DateTime?>(banKey);
            if (cachedBanEndTime.HasValue)
            {
                var remaining = cachedBanEndTime.Value - DateTime.Now;

                if (remaining.TotalMinutes > 0)
                {
                    var remainingText = remaining.TotalDays >= 1
                        ? $"{Math.Ceiling(remaining.TotalDays)} gün"
                        : $"{Math.Ceiling(remaining.TotalMinutes)} dakika";

                    return new ErrorResult($"Çok fazla kayıt denemesi. Lütfen {remainingText} sonra tekrar deneyin.");
                }
                else
                {
                    await _cacheService.RemoveAsync(banKey);
                }
            }

            return new SuccessResult();
        }

        [LogAspect]
        [PerformanceAspect(3)]
        public async Task<IResult> RecordSuccessfulRegisterAsync(string ipAddress, CancellationToken ct = default)
        {
            ct.ThrowIfCancellationRequested();
            var countKey = GetRegisterCountKey(ipAddress);
            var banKey = GetRegisterBanKey(ipAddress);
            var violationKey = GetRegisterViolationKey(ipAddress);

            int registerCount = await _cacheService.GetAsync<int>(countKey);
            registerCount++;

            if (registerCount >= MAX_REGISTER_ATTEMPTS)
            {
                // Violation sayısını al
                int violationCount = await _cacheService.GetAsync<int>(violationKey);

                // Progressive ban süresi hesapla
                int banDuration = GetProgressiveBanDuration(violationCount, REGISTER_BAN_DURATIONS);
                var banEndTime = DateTime.Now.AddMinutes(banDuration);

                // Ban uygula
                await _cacheService.SetAsync(banKey, banEndTime, TimeSpan.FromMinutes(banDuration));

                // Violation sayısını artır
                violationCount++;
                await _cacheService.SetAsync(violationKey, violationCount, TimeSpan.FromMinutes(20160)); // 14 gün boyunca violation sayısını tut

                // Register count'u sıfırla
                await _cacheService.RemoveAsync(countKey);

                var banText = banDuration >= 1440
                    ? $"{banDuration / 1440} gün"
                    : $"{banDuration / 60} saat";

                return new ErrorResult($"Günlük kayıt limitini aştınız. {banText} boyunca kayıt yapamazsınız.");
            }
            else
            {
                // Register count'u güncelle (24 saat boyunca tut)
                await _cacheService.SetAsync(countKey, registerCount, TimeSpan.FromMinutes(1440));
            }

            return new SuccessResult();
        }

        [LogAspect]
        [PerformanceAspect(3)]
        public async Task<IDataResult<int>> GetRemainingRegisterBanTimeAsync(string ipAddress, CancellationToken ct = default)
        {
            ct.ThrowIfCancellationRequested();
            var banKey = GetRegisterBanKey(ipAddress);

            var cachedBanEndTime = await _cacheService.GetAsync<DateTime?>(banKey);
            if (cachedBanEndTime.HasValue)
            {
                var remaining = cachedBanEndTime.Value - DateTime.Now;

                if (remaining.TotalMinutes > 0)
                {
                    return new SuccessDataResult<int>((int)Math.Ceiling(remaining.TotalMinutes));
                }
            }

            return new SuccessDataResult<int>(0);
        }

        [LogAspect]
        [PerformanceAspect(3)]
        public async Task<IResult> CheckProfileImageUploadAttemptAsync(int userId, CancellationToken ct = default)
        {
            ct.ThrowIfCancellationRequested();
            var uploadCountKey = GetProfileImageUploadCountKey(userId);

            int uploadCount = await _cacheService.GetAsync<int>(uploadCountKey);

            if (uploadCount >= MAX_PROFILE_IMAGE_UPLOADS_PER_DAY)
            {
                return new ErrorResult($"Günlük profil fotoğrafı yükleme limitini aştınız. Günde en fazla {MAX_PROFILE_IMAGE_UPLOADS_PER_DAY} fotoğraf yükleyebilirsiniz.");
            }

            return new SuccessResult();
        }

        [LogAspect]
        [PerformanceAspect(3)]
        public async Task<IResult> RecordProfileImageUploadAsync(int userId, CancellationToken ct = default)
        {
            ct.ThrowIfCancellationRequested();
            var uploadCountKey = GetProfileImageUploadCountKey(userId);

            int uploadCount = await _cacheService.GetAsync<int>(uploadCountKey);
            uploadCount++;

            if (uploadCount > MAX_PROFILE_IMAGE_UPLOADS_PER_DAY)
            {
                return new ErrorResult($"Günlük profil fotoğrafı yükleme limitini aştınız. Günde en fazla {MAX_PROFILE_IMAGE_UPLOADS_PER_DAY} fotoğraf yükleyebilirsiniz.");
            }

            // Günün sonuna kadar cache'te tut (gece yarısına kadar)
            var endOfDay = DateTime.Today.AddDays(1);
            var minutesUntilEndOfDay = (int)(endOfDay - DateTime.Now).TotalMinutes;

            await _cacheService.SetAsync(uploadCountKey, uploadCount, TimeSpan.FromMinutes(minutesUntilEndOfDay));

            return new SuccessResult();
        }

        [LogAspect]
        [PerformanceAspect(3)]
        public async Task<IDataResult<int>> GetRemainingProfileImageUploadsAsync(int userId, CancellationToken ct = default)
        {
            ct.ThrowIfCancellationRequested();
            var uploadCountKey = GetProfileImageUploadCountKey(userId);

            int uploadCount = await _cacheService.GetAsync<int>(uploadCountKey);

            int remaining = MAX_PROFILE_IMAGE_UPLOADS_PER_DAY - uploadCount;
            return new SuccessDataResult<int>(Math.Max(0, remaining));
        }

        [LogAspect]
        [PerformanceAspect(3)]
        public async Task<IResult> CheckFileDownloadAttemptAsync(int userId, CancellationToken ct = default)
        {
            ct.ThrowIfCancellationRequested();
            var downloadCountKey = GetFileDownloadCountKey(userId);

            int downloadCount = await _cacheService.GetAsync<int>(downloadCountKey);

            if (downloadCount >= MAX_FILE_DOWNLOADS_PER_10_MINUTES)
            {
                return new ErrorResult($"Dosya indirme limitini aştınız. 10 dakikada en fazla {MAX_FILE_DOWNLOADS_PER_10_MINUTES} dosya indirebilirsiniz.");
            }

            return new SuccessResult();
        }

        [LogAspect]
        [PerformanceAspect(3)]
        public async Task<IResult> RecordFileDownloadAsync(int userId, CancellationToken ct = default)
        {
            ct.ThrowIfCancellationRequested();
            var downloadCountKey = GetFileDownloadCountKey(userId);

            int downloadCount = await _cacheService.GetAsync<int>(downloadCountKey);
            downloadCount++;

            if (downloadCount > MAX_FILE_DOWNLOADS_PER_10_MINUTES)
            {
                return new ErrorResult($"Dosya indirme limitini aştınız. 10 dakikada en fazla {MAX_FILE_DOWNLOADS_PER_10_MINUTES} dosya indirebilirsiniz.");
            }

            // 10 dakika boyunca cache'te tut
            await _cacheService.SetAsync(downloadCountKey, downloadCount, TimeSpan.FromMinutes(10));

            return new SuccessResult();
        }

        [LogAspect]
        [PerformanceAspect(3)]
        public async Task<IDataResult<int>> GetRemainingFileDownloadsAsync(int userId, CancellationToken ct = default)
        {
            ct.ThrowIfCancellationRequested();
            var downloadCountKey = GetFileDownloadCountKey(userId);

            int downloadCount = await _cacheService.GetAsync<int>(downloadCountKey);

            int remaining = MAX_FILE_DOWNLOADS_PER_10_MINUTES - downloadCount;
            return new SuccessDataResult<int>(Math.Max(0, remaining));
        }

        public async Task<string> GenerateDeviceFingerprintAsync(string ipAddress, string userAgent, string deviceInfo, CancellationToken ct = default)
        {
            ct.ThrowIfCancellationRequested();
            var combinedInfo = $"{ipAddress}|{userAgent}|{deviceInfo}";
            using (var sha256 = SHA256.Create())
            {
                var hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(combinedInfo));
                return await Task.FromResult(Convert.ToBase64String(hashBytes));
            }
        }

        // Private Helper Methods
        private int GetProgressiveBanDuration(int violationCount, int[] banDurations)
        {
            if (violationCount >= banDurations.Length)
            {
                return banDurations[banDurations.Length - 1]; // Son değeri kullan
            }
            return banDurations[violationCount];
        }

        private string GetLoginFailKey(string ipAddress, string deviceFingerprint)
        {
            return $"login_fail:{ipAddress}:{deviceFingerprint}";
        }

        private string GetLoginBanKey(string ipAddress, string deviceFingerprint)
        {
            return $"login_ban:{ipAddress}:{deviceFingerprint}";
        }

        private string GetLoginViolationKey(string ipAddress, string deviceFingerprint)
        {
            return $"login_violation:{ipAddress}:{deviceFingerprint}";
        }

        private string GetRegisterCountKey(string ipAddress)
        {
            var today = DateTime.Now.ToString("yyyy-MM-dd");
            return $"register_count:{ipAddress}:{today}";
        }

        private string GetRegisterBanKey(string ipAddress)
        {
            return $"register_ban:{ipAddress}";
        }

        private string GetRegisterViolationKey(string ipAddress)
        {
            return $"register_violation:{ipAddress}";
        }

        private string GetProfileImageUploadCountKey(int userId)
        {
            var today = DateTime.Now.ToString("yyyy-MM-dd");
            return $"profile_image_upload:{userId}:{today}";
        }

        private string GetFileDownloadCountKey(int userId)
        {
            var tenMinuteSlot = DateTime.Now.ToString("yyyy-MM-dd-HH") + "-" + (DateTime.Now.Minute / 10);
            return $"file_download:{userId}:{tenMinuteSlot}";
        }
    }
}
