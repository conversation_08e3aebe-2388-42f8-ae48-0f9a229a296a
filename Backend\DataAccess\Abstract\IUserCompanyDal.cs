﻿using Core.DataAccess;
using Core.Utilities.Results;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Threading;


namespace DataAccess.Abstract
{
    public interface IUserCompanyDal:IEntityRepository<UserCompany>
    {


        // SOLID prensiplerine uygun: Business logic DAL katmanına taşındı

        // ASYNC imzalar (CT ile)
        Task<List<UserCompanyDetailDto>> GetUserCompanyDetailsAsync(CancellationToken ct = default);
        Task<int> GetUserCompanyIdAsync(int userId, CancellationToken ct = default);
        Task<List<UserCompany>> GetActiveUserCompaniesAsync(int userId, CancellationToken ct = default);
        Task<IResult> AddUserCompanyWithValidationAsync(UserCompany userCompany, CancellationToken ct = default);
        Task<IResult> UpdateActiveCompanyWithValidationAsync(int userId, int companyId, CancellationToken ct = default);


    }
}
