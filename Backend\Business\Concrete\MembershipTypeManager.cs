﻿﻿using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Business.ValidationRules.FluentValidation;

using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Validation;
using Core.Aspects.Autofac.Transaction;
using Core.Utilities.Results;
using Core.Utilities.Paging;
using Core.Utilities.Security.CompanyContext;
using DataAccess.Abstract;
using DataAccess.Concrete.EntityFramework;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Business.Concrete
{
    public class MembershipTypeManager : IMembershipTypeService
    {
        IMembershiptypeDal _membershipTypeDal;
        private readonly ICompanyContext _companyContext;

        public MembershipTypeManager(IMembershiptypeDal membershipTypeDal, ICompanyContext companyContext)
        {
            _membershipTypeDal = membershipTypeDal;
            _companyContext = companyContext;
        }

        [SecuredOperation("owner,admin")]
        [ValidationAspect(typeof(MembershipTypeValidator))]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("MembershipType")]
        public async Task<IResult> AddAsync(MembershipType membershipType, CancellationToken ct = default)
        {
            await _membershipTypeDal.AddAsync(membershipType, ct);
            return new SuccessResult(Messages.MembershipTypeAdded);
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [SmartCacheRemoveAspectAsync("MembershipType")]
        public async Task<IResult> DeleteAsync(int id, CancellationToken ct = default)
        {
            return await _membershipTypeDal.SoftDeleteMembershipTypeAsync(id, _companyContext.GetCompanyId(), ct);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(14400)]
        public async Task<IDataResult<List<MembershipType>>> GetAllAsync(CancellationToken ct = default)
        {
            var list = await _membershipTypeDal.GetAllAsync(m => m.IsActive == true, ct);
            return new SuccessDataResult<List<MembershipType>>(list);
        }


        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(7200)]
        public async Task<IDataResult<PaginatedResult<MembershipType>>> GetAllPaginatedAsync(MembershipTypePagingParameters parameters, CancellationToken ct = default)
        {
            var result = await _membershipTypeDal.GetAllPaginatedAsync(parameters, ct);
            return new SuccessDataResult<PaginatedResult<MembershipType>>(result);
        }

        [SecuredOperation("owner,admin")]
        [ValidationAspect(typeof(MembershipTypeValidator))]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("MembershipType")]
        public async Task<IResult> UpdateAsync(MembershipType membershipType, CancellationToken ct = default)
        {
            return await _membershipTypeDal.UpdateMembershipTypeWithBusinessLogicAsync(membershipType, _companyContext.GetCompanyId(), ct);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(14400)]
        public async Task<IDataResult<List<BranchGetAllDto>>> GetBranchesAndTypesAsync(CancellationToken ct = default)
        {
            var result = await _membershipTypeDal.GetBranchesAndTypesAsync(ct);
            return new SuccessDataResult<List<BranchGetAllDto>>(result);
        }


        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(7200)]
        public async Task<IDataResult<List<PackageWithCountDto>>> GetPackagesByBranchAsync(string branch, CancellationToken ct = default)
        {
            var list = await _membershipTypeDal.GetPackagesByBranchAsync(branch, ct);
            return new SuccessDataResult<List<PackageWithCountDto>>(list);
        }
    }
}
