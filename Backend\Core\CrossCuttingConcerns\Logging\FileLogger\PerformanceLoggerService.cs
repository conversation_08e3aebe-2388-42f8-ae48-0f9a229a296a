﻿using System;
using System.IO;
using System.Threading;
using System.Threading.Tasks;


namespace Core.CrossCuttingConcerns.Logging.FileLogger
{
    public class PerformanceLoggerService : ILogService
    {
        private readonly string _logDirectory;
        private readonly string _performanceLogDirectory;
        private readonly object _lock = new object();

        public PerformanceLoggerService()
        {
            _logDirectory = @"C:\GymProjectLogs";
            _performanceLogDirectory = Path.Combine(_logDirectory, "PerformanceAspectLogs");
            Directory.CreateDirectory(_performanceLogDirectory);
        }

        private void Log(string level, string message)
        {
            var logFilePath = Path.Combine(_performanceLogDirectory, $"performance_log_{DateTime.Now:dd-MM-yyyy}.txt");
            var logMessage = $"{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff} [{level}] {message}";

            _ = AppendLineAsync(logFilePath, logMessage + Environment.NewLine);
        }

        private static async Task AppendLineAsync(string path, string content, CancellationToken ct = default)
        {
            try
            {
                await using var stream = new FileStream(path, FileMode.Append, FileAccess.Write, FileShare.Read, 4096, useAsync: true);
                await using var writer = new StreamWriter(stream);
                await writer.WriteAsync(content.AsMemory(), ct).ConfigureAwait(false);
                await writer.FlushAsync().ConfigureAwait(false);
            }
            catch
            {
                // Sessizce yut
            }
        }

        public void Info(string message, bool isPerformanceLog = false)
        {
            Log("INFO", message);
        }

        public void Debug(string message)
        {
            Log("DEBUG", message);
        }

        public void Warn(string message)
        {
            Log("WARN", message);
        }

        public void Error(string message)
        {
            Log("ERROR", message);
        }

        public void Fatal(string message)
        {
            Log("FATAL", message);
        }

        public void LogPerformance(string message)
        {
            Log("PERFORMANCE", message);
        }
    }
}