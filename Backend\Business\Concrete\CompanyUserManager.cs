﻿using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Business.ValidationRules.FluentValidation;
using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Validation;
using Core.Aspects.Autofac.Transaction;

using Core.Entities.Concrete;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Business.Concrete
{
    public class CompanyUserManager : ICompanyUserService
    {
        ICompanyUserDal _companyUserDal;

        public CompanyUserManager(ICompanyUserDal companyUserDal)
        {
            _companyUserDal = companyUserDal;
        }


        // ASYNC versiyonlar
        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        [CacheAspect(3600)]
        public async Task<IDataResult<List<CompanyUser>>> GetAllAsync(CancellationToken ct = default)
        {
            var list = await _companyUserDal.GetAllAsync(null, ct);
            return new SuccessDataResult<List<CompanyUser>>(list, Messages.CompanyUserGetAll);
        }

        [SecuredOperation("owner")]
        [ValidationAspect(typeof(CompanyUserValidator))]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("CompanyUser")]
        public async Task<IResult> AddAsync(CompanyUser companyUser, CancellationToken ct = default)
        {
            await _companyUserDal.AddAsync(companyUser, ct);
            return new SuccessResult(Messages.UserAdded);
        }

        [SecuredOperation("owner")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("CompanyUser")]
        public async Task<IResult> DeleteAsync(int id, CancellationToken ct = default)
        {
            await _companyUserDal.DeleteAsync(id, ct);
            return new SuccessResult(Messages.UserDeleted);
        }

        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        [CacheAspect(3600)]
        public async Task<IDataResult<List<CompanyUser>>> GetByCityIdAsync(int cityId, CancellationToken ct = default)
        {
            var list = await _companyUserDal.GetAllAsync(c => c.CityID == cityId, ct);
            return new SuccessDataResult<List<CompanyUser>>(list);
        }

        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        [CacheAspect(3600)]
        public async Task<IDataResult<List<CompanyUserDetailDto>>> GetCompanyUserDetailsAsync(CancellationToken ct = default)
        {
            var list = await _companyUserDal.GetCompanyUserDetailsAsync(ct);
            return new SuccessDataResult<List<CompanyUserDetailDto>>(list);
        }

        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        [CacheAspect(3600)]
        public async Task<IDataResult<List<CompanyDetailDto>>> GetCompanyDetailsAsync(CancellationToken ct = default)
        {
            var list = await _companyUserDal.GetCompanyDetailsAsync(ct);
            return new SuccessDataResult<List<CompanyDetailDto>>(list);
        }

        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        [CacheAspect(3600)]
        public async Task<IDataResult<List<CompanyDetailDto>>> GetCompanyUserDetailsByCityIdAsync(int cityId, CancellationToken ct = default)
        {
            var list = await _companyUserDal.GetCompanyUserDetailsByCityIdAsync(cityId, ct);
            return new SuccessDataResult<List<CompanyDetailDto>>(list);
        }

        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        [CacheAspect(3600)]
        public async Task<IDataResult<CompanyUser>> GetByIdAsync(int companyUserID, CancellationToken ct = default)
        {
            var entity = await _companyUserDal.GetAsync(cu => cu.CompanyUserID == companyUserID, ct);
            if (entity == null)
            {
                return new ErrorDataResult<CompanyUser>("Şirket kullanıcısı bulunamadı");
            }
            return new SuccessDataResult<CompanyUser>(entity);
        }

        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        [CacheAspect(3600)]
        public async Task<IDataResult<CompanyUserFullDetailDto>> GetCompanyUserFullDetailsAsync(int companyUserID, CancellationToken ct = default)
        {
            var dto = await _companyUserDal.GetCompanyUserFullDetailsAsync(companyUserID, ct);
            if (dto == null)
            {
                return new ErrorDataResult<CompanyUserFullDetailDto>("Kullanıcı detayları bulunamadı");
            }
            return new SuccessDataResult<CompanyUserFullDetailDto>(dto);
        }

        [SecuredOperation("owner")]
        [ValidationAspect(typeof(CompanyUserValidator))]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("CompanyUser")]
        public async Task<IResult> UpdateAsync(CompanyUser companyUser, CancellationToken ct = default)
        {
            await _companyUserDal.UpdateAsync(companyUser, ct);
            return new SuccessResult(Messages.UserUpdated);
        }

        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        [CacheAspect(1800)]
        public async Task<IDataResult<PaginatedCompanyUserDto>> GetCompanyUsersPaginatedAsync(int pageNumber, int pageSize, string searchTerm = "", CancellationToken ct = default)
        {
            var result = await _companyUserDal.GetCompanyUsersPaginatedAsync(pageNumber, pageSize, searchTerm, ct);
            return new SuccessDataResult<PaginatedCompanyUserDto>(result);
        }

        [SecuredOperation("owner")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("CompanyUser")]
        public async Task<IResult> SoftDeleteCompanyUserAsync(int companyUserID, CancellationToken ct = default)
        {
            var result = await _companyUserDal.SoftDeleteCompanyUserBasicAsync(companyUserID, ct);
            if (result.Success)
            {
                return new SuccessResult("Salon başarıyla pasif hale getirildi. Pasif salonlar bölümünden geri yükleyebilirsiniz.");
            }
            return new ErrorResult(result.Message);
        }

        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        [CacheAspect(1800)]
        public async Task<IDataResult<List<DeletedCompanyUserDto>>> GetDeletedCompanyUsersAsync(CancellationToken ct = default)
        {
            var list = await _companyUserDal.GetDeletedCompanyUsersAsync(ct);
            return new SuccessDataResult<List<DeletedCompanyUserDto>>(list, "Pasif salonlar listelendi");
        }

        [SecuredOperation("owner")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("CompanyUser")]
        public async Task<IResult> RestoreCompanyUserAsync(int companyUserID, CancellationToken ct = default)
        {
            var result = await _companyUserDal.RestoreCompanyUserBasicAsync(companyUserID, ct);
            if (result.Success)
            {
                return new SuccessResult("Salon başarıyla geri yüklendi.");
            }
            return new ErrorResult(result.Message);
        }

        [SecuredOperation("owner")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("CompanyUser")]
        public async Task<IResult> UpdateCompanyUserFullAsync(CompanyUserFullUpdateDto updateDto, CancellationToken ct = default)
        {
            var result = await _companyUserDal.UpdateCompanyUserFullAsync(updateDto, ct);
            return result;
        }

    }
}
