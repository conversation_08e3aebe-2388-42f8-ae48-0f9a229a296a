﻿﻿using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Core.Aspects.Autofac.Performance;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace Business.Concrete
{
    public class EntryExitHistoryManager : IEntryExitHistoryService
    {
        IEntryExitHistoryDal _entryExitHistoryDal;


        //buradaki hiç bir şeye securedoperation aspecti ayarlama qr kod okununca buralara girmesi gerekiyor ve yetkisiz erişim olmalı
        public EntryExitHistoryManager(IEntryExitHistoryDal entryExitHistoryDal)
        {
            _entryExitHistoryDal = entryExitHistoryDal;
        }
        [PerformanceAspect(3)]
        public async Task<IResult> AddAsync(EntryExitHistory entryExitHistory, CancellationToken ct = default)
        {
            await _entryExitHistoryDal.AddAsync(entryExitHistory, ct);
            return new SuccessResult(Messages.EntryHistoryAdded);
        }

        // QR kod taraması için özel async metot - CompanyID parametreli
        [PerformanceAspect(3)]
        public async Task<IResult> AddWithCompanyIdAsync(EntryExitHistory entryExitHistory, int companyId, CancellationToken ct = default)
        {
            return await _entryExitHistoryDal.AddWithCompanyIdAsync(entryExitHistory, companyId, ct);
        }
        [PerformanceAspect(3)]
        public async Task<IResult> DeleteAsync(int id, CancellationToken ct = default)
        {
            await _entryExitHistoryDal.DeleteAsync(id, ct);
            return new SuccessResult(Messages.EntryExitHistoryDeleted);
        }
        [PerformanceAspect(3)]
        public async Task<IDataResult<List<EntryExitHistory>>> GetAllAsync(CancellationToken ct = default)
        {
            var list = await _entryExitHistoryDal.GetAllAsync(null, ct);
            return new SuccessDataResult<List<EntryExitHistory>>(list);
        }
        [PerformanceAspect(3)]
        public async Task<IResult> UpdateAsync(EntryExitHistory entryExitHistory, CancellationToken ct = default)
        {
            await _entryExitHistoryDal.UpdateAsync(entryExitHistory, ct);
            return new SuccessResult(Messages.EntryExitHistoryUpdated);
        }

        // QR kod taraması için özel async güncelleme metodu - CompanyID parametreli
        [PerformanceAspect(3)]
        public async Task<IResult> UpdateWithCompanyIdAsync(EntryExitHistory entryExitHistory, int companyId, CancellationToken ct = default)
        {
            return await _entryExitHistoryDal.UpdateWithCompanyIdAsync(entryExitHistory, companyId, ct);
        }

    }
}
