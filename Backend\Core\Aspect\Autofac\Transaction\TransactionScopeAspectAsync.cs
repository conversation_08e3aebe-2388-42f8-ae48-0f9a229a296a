using Castle.DynamicProxy;
using Core.Utilities.Interceptors;
using System;
using System.Threading.Tasks;
using System.Transactions;

namespace Core.Aspects.Autofac.Transaction
{
    /// <summary>
    /// Async akışlar için TransactionScope aspect.
    /// TransactionScopeAsyncFlowOption.Enabled ile await zincirinde akışı korur.
    /// </summary>
    public class TransactionScopeAspectAsync : MethodInterceptionBaseAttribute
    {
        private readonly TransactionScopeOption _option;
        private readonly IsolationLevel _isolationLevel;
        private readonly TimeSpan _scopeTimeout;

        public TransactionScopeAspectAsync(
            TransactionScopeOption option = TransactionScopeOption.Required,
            IsolationLevel isolationLevel = IsolationLevel.ReadCommitted,
            int timeoutInSeconds = 60)
        {
            _option = option;
            _isolationLevel = isolationLevel;
            _scopeTimeout = TimeSpan.FromSeconds(timeoutInSeconds);
        }

        public override void Intercept(IInvocation invocation)
        {
            var returnType = invocation.Method.ReturnType;

            // TransactionScope'u başlat ve invocation'ı çalıştır
            var txOptions = new TransactionOptions { IsolationLevel = _isolationLevel, Timeout = _scopeTimeout };
            var scope = new TransactionScope(_option, txOptions, TransactionScopeAsyncFlowOption.Enabled);

            try
            {
                invocation.Proceed();
            }
            catch
            {
                // Sync istisna olduysa scope dispose ile rollback
                scope.Dispose();
                throw;
            }

            // Task
            if (returnType == typeof(Task))
            {
                var task = (Task)invocation.ReturnValue;
                invocation.ReturnValue = InterceptTask(task, scope);
                return;
            }

            // Task<T>
            if (returnType.IsGenericType && returnType.GetGenericTypeDefinition() == typeof(Task<>))
            {
                var task = invocation.ReturnValue;
                var method = typeof(TransactionScopeAspectAsync).GetMethod(nameof(InterceptTaskWithResult), System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Static);
                var genericMethod = method.MakeGenericMethod(returnType.GetGenericArguments()[0]);
                invocation.ReturnValue = genericMethod.Invoke(null, new object[] { task, scope });
                return;
            }

            // Sync dönüş: başarı → complete, sonunda dispose
            scope.Complete();
            scope.Dispose();
        }

        private static async Task InterceptTask(Task task, TransactionScope scope)
        {
            try
            {
                await task.ConfigureAwait(false);
                scope.Complete();
            }
            finally
            {
                scope.Dispose();
            }
        }

        private static async Task<T> InterceptTaskWithResult<T>(Task<T> task, TransactionScope scope)
        {
            try
            {
                var result = await task.ConfigureAwait(false);
                scope.Complete();
                return result;
            }
            finally
            {
                scope.Dispose();
            }
        }
    }
}

