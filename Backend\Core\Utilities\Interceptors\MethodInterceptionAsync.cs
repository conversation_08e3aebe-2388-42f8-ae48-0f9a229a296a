using Castle.DynamicProxy;
using System;
using System.Threading.Tasks;

namespace Core.Utilities.Interceptors
{
    /// <summary>
    /// Async metodlar için interception tabanı.
    /// OnBefore → await → OnAfter/OnSuccess/OnException sırasını garanti eder.
    /// Sync metotlarda sync akış ile degrade olur.
    /// </summary>
    public abstract class MethodInterceptionAsync : MethodInterceptionBaseAttribute
    {
        protected virtual void OnBefore(IInvocation invocation) { }
        protected virtual void OnAfter(IInvocation invocation) { }
        protected virtual void OnException(IInvocation invocation, Exception e) { }
        protected virtual void OnSuccess(IInvocation invocation) { }

        protected virtual Task OnAfterAsync(IInvocation invocation) => Task.CompletedTask;
        protected virtual Task OnExceptionAsync(IInvocation invocation, Exception e) => Task.CompletedTask;
        protected virtual Task OnSuccessAsync(IInvocation invocation) => Task.CompletedTask;

        public override void Intercept(IInvocation invocation)
        {
            var returnType = invocation.Method.ReturnType;

            // OnBefore (sync)
            OnBefore(invocation);

            try
            {
                invocation.Proceed();
            }
            catch (Exception ex)
            {
                // Sync istisna yakalandı (Task dönmeden önce)
                try { OnException(invocation, ex); } finally { OnAfter(invocation); }
                throw;
            }

            // Async türler: Task ve Task<T>
            if (returnType == typeof(Task))
            {
                var task = (Task)invocation.ReturnValue;
                invocation.ReturnValue = InterceptTask(task, invocation);
                return;
            }

            if (returnType.IsGenericType && returnType.GetGenericTypeDefinition() == typeof(Task<>))
            {
                var task = invocation.ReturnValue;
                var method = typeof(MethodInterceptionAsync).GetMethod(nameof(InterceptTaskWithResult), System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                var genericMethod = method.MakeGenericMethod(returnType.GetGenericArguments()[0]);
                invocation.ReturnValue = genericMethod.Invoke(this, new object[] { task, invocation });
                return;
            }

            // Sync metot akışı
            try
            {
                OnSuccess(invocation);
            }
            finally
            {
                OnAfter(invocation);
            }
        }

        private async Task InterceptTask(Task task, IInvocation invocation)
        {
            try
            {
                await task.ConfigureAwait(false);
                await OnSuccessAsync(invocation).ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                await OnExceptionAsync(invocation, ex).ConfigureAwait(false);
                throw;
            }
            finally
            {
                await OnAfterAsync(invocation).ConfigureAwait(false);
            }
        }

        private async Task<T> InterceptTaskWithResult<T>(Task<T> task, IInvocation invocation)
        {
            try
            {
                var result = await task.ConfigureAwait(false);
                await OnSuccessAsync(invocation).ConfigureAwait(false);
                return result;
            }
            catch (Exception ex)
            {
                await OnExceptionAsync(invocation, ex).ConfigureAwait(false);
                throw;
            }
            finally
            {
                await OnAfterAsync(invocation).ConfigureAwait(false);
            }
        }
    }
}

