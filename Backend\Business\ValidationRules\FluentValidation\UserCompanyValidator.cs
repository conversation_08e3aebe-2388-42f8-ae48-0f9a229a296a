﻿using Core.Utilities.IoC;
using DataAccess.Abstract;
using DataAccess.Concrete.EntityFramework;
using Entities.Concrete;
using FluentValidation;
using Microsoft.Extensions.DependencyInjection;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Business.ValidationRules.FluentValidation
{
    public class UserCompanyValidator : AbstractValidator<UserCompany>
    {
        private readonly IUserCompanyDal _userCompanyDal;

        public UserCompanyValidator()
        {
            // ServiceTool üzerinden servisleri al
            _userCompanyDal = ServiceTool.ServiceProvider?.GetService<IUserCompanyDal>();

            RuleFor(p => p.UserID).NotEmpty().WithMessage("Kullanıcı seçimi boş bırakılamaz.");
            RuleFor(p => p.CompanyId).NotEmpty().WithMessage("Şirket seçimi boş bırakılamaz.");
            RuleFor(p => p).MustAsync(BeUniqueCompanyNameAsync).WithMessage("Bu şirket zaten bir kullanıcıya bağlı!");
        }

        private async Task<bool> BeUniqueCompanyNameAsync(UserCompany userCompany, CancellationToken ct)
        {
            // Null check for DI services - 1000+ salon sisteminde kritik
            if (_userCompanyDal == null)
            {
                // Log the issue for monitoring in production
                System.Diagnostics.Debug.WriteLine("UserCompanyValidator: _userCompanyDal is null - validation bypassed");
                return true; // Validation geçer, başka katmanda kontrol edilir
            }

            // DI kullanılıyor - Scalability optimized
            if (userCompany.UserCompanyID != 0)
            {
                // Güncelleme durumu
                var list = await _userCompanyDal.GetAllAsync(uc =>
                    uc.CompanyId == userCompany.CompanyId &&
                    uc.UserCompanyID != userCompany.UserCompanyID &&
                    uc.IsActive == true, ct);
                return !list.Any();
            }
            else
            {
                // Yeni ekleme durumu
                var list = await _userCompanyDal.GetAllAsync(uc =>
                    uc.CompanyId == userCompany.CompanyId &&
                    uc.IsActive == true, ct);
                return !list.Any();
            }
        }
    }
}