﻿using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Business.ValidationRules.FluentValidation;
using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Transaction;
using Core.Aspects.Autofac.Validation;
using Core.Entities.Concrete;
using Core.Utilities.Business;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using System.Security.Claims;
using Core.Extensions;


namespace Business.Concrete
{
    public class UserManager : IUserService
    {
        private readonly IUserDal _userDal;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public UserManager(IUserDal userDal, IHttpContextAccessor httpContextAccessor)
        {
            _userDal = userDal;
            _httpContextAccessor = httpContextAccessor;
        }



        [SecuredOperation("owner")]
        [ValidationAspect(typeof(UserValidator))]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("User")]
        public async Task<IResult> AddAsync(User user, CancellationToken ct = default)
        {
            await _userDal.AddAsync(user, ct);
            return new SuccessResult(Messages.UserAdded);
        }


        [PerformanceAspect(3)]
        public async Task<List<OperationClaim>> GetClaimsAsync(User user, CancellationToken ct = default)
        {
            return await _userDal.GetClaimsAsync(user, ct);
        }



        [SecuredOperation("owner")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("User")]
        public async Task<IResult> DeleteAsync(int id, CancellationToken ct = default)
        {
            await _userDal.DeleteAsync(id, ct);
            return new SuccessResult(Messages.UserDeleted);
        }



        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        [CacheAspect(3600)]
        public async Task<IDataResult<List<User>>> GetAllAsync(CancellationToken ct = default)
        {
            var list = await _userDal.GetAllAsync(null, ct);
            return new SuccessDataResult<List<User>>(list);
        }



        [PerformanceAspect(3)]
        public async Task<User> GetByMailAsync(string email, CancellationToken ct = default)
        {
            return await _userDal.GetAsync(u => u.Email == email, ct);
        }



        [SecuredOperation("owner")]
        [ValidationAspect(typeof(UserValidator))]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("User")]
        public async Task<IResult> UpdateAsync(User user, CancellationToken ct = default)
        {
            var oldUser = await _userDal.GetAsync(u => u.UserID == user.UserID, ct);

            await _userDal.UpdateAsync(user, ct);

            if (oldUser != null &&
                (oldUser.Email != user.Email ||
                 oldUser.FirstName != user.FirstName ||
                 oldUser.LastName != user.LastName))
            {
                var syncResult = await _userDal.SyncCompanyUserDataAsync(user, oldUser, ct);
                if (!syncResult.Success)
                {
                    System.Diagnostics.Debug.WriteLine($"CompanyUser sync warning: {syncResult.Message}");
                }
            }

            return new SuccessResult(Messages.UserUpdated);
        }

        //[SecuredOperation("owner,admin")]


        [PerformanceAspect(3)]
        public async Task<IDataResult<User>> GetByIdAsync(int userId, CancellationToken ct = default)
        {
            return await _userDal.GetUserByIdWithValidationAsync(userId, ct);
        }

        /// <summary>
        /// Member rolü olmayan kullanıcıları getirir (Lisans satın alma için)
        /// </summary>



        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(1800)]
        public async Task<IDataResult<List<User>>> GetNonMembersAsync(CancellationToken ct = default)
        {
            try
            {
                var users = await _userDal.GetNonMembersAsync(ct);
                return new SuccessDataResult<List<User>>(users, "Member olmayan kullanıcılar başarıyla getirildi.");
            }
            catch (OperationCanceledException)
            {
                throw;
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<List<User>>("Kullanıcılar getirilirken bir hata oluştu: " + ex.Message);
            }
        }

        /// <summary>
        /// Member rolü olmayan kullanıcıları sayfalı olarak getirir (10K+ kullanıcı için)
        /// </summary>



        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public async Task<IDataResult<List<User>>> GetNonMembersPaginatedAsync(int page, int pageSize, string searchTerm, CancellationToken ct = default)
        {
            try
            {
                if (page < 1) page = 1;
                if (pageSize < 1 || pageSize > 100) pageSize = 20;

                var users = await _userDal.GetNonMembersPaginatedAsync(page, pageSize, searchTerm ?? string.Empty, ct);
                return new SuccessDataResult<List<User>>(users, "Kullanıcılar başarıyla getirildi.");
            }
            catch (OperationCanceledException)
            {
                throw;
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<List<User>>("Kullanıcılar getirilirken bir hata oluştu: " + ex.Message);
            }
        }

        /// <summary>
        /// Member rolü olmayan kullanıcı sayısını getirir
        /// </summary>



        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public async Task<IDataResult<int>> GetNonMembersCountAsync(string searchTerm, CancellationToken ct = default)
        {
            try
            {
                var count = await _userDal.GetNonMembersCountAsync(searchTerm ?? string.Empty, ct);
                return new SuccessDataResult<int>(count, "Kullanıcı sayısı başarıyla getirildi.");
            }
            catch (OperationCanceledException)
            {
                throw;
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<int>("Kullanıcı sayısı getirilirken bir hata oluştu: " + ex.Message);
            }
        }

        [SecuredOperation("owner,admin,member")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("User")]
        public async Task<IResult> UpdatePasswordAsync(int targetUserId, byte[] passwordHash, byte[] passwordSalt, bool requirePasswordChange, CancellationToken ct = default)
        {
            // Self-only kontrol: owner dışı roller sadece kendi şifresini güncelleyebilir
            var user = _httpContextAccessor.HttpContext?.User;
            if (user == null || !user.Identity?.IsAuthenticated == true)
            {
                return new ErrorResult("Oturum bulunamadı");
            }

            var callerIdStr = user.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (!int.TryParse(callerIdStr, out var callerId))
            {
                return new ErrorResult("Kullanıcı bilgisi okunamadı");
            }

            var callerRoles = user.ClaimRoles() ?? new List<string>();
            var isOwner = callerRoles.Contains("owner");

            if (!isOwner && callerId != targetUserId)
            {
                return new ErrorResult("Sadece kendi şifrenizi güncelleyebilirsiniz.");
            }

            var dbUser = await _userDal.GetAsync(u => u.UserID == targetUserId, ct);
            if (dbUser == null)
            {
                return new ErrorResult(Messages.UserNotFound);
            }

            dbUser.PasswordHash = passwordHash;
            dbUser.PasswordSalt = passwordSalt;
            dbUser.RequirePasswordChange = requirePasswordChange;
            dbUser.UpdatedDate = DateTime.Now;

            await _userDal.UpdateAsync(dbUser, ct);
            return new SuccessResult("Şifre güncellendi.");
        }

    }
}
