﻿using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Business.ValidationRules.FluentValidation;
using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Validation;
using Core.Utilities.Results;
using DataAccess.Abstract;
using DataAccess.Concrete.EntityFramework;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Business.Concrete
{
    public class CompanyManager : ICompanyService
    {
        ICompanyDal _companyDal;

        public CompanyManager(ICompanyDal companyDal)
        {
            _companyDal = companyDal;
        }
        [SecuredOperation("owner")]
        [ValidationAspect(typeof(CompanyValidator))]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("Company")]
        public async Task<IResult> AddAsync(Company company, CancellationToken ct = default)
        {
            await _companyDal.AddAsync(company, ct);
            return new SuccessResult(Messages.CompanyAdded);
        }
        [SecuredOperation("owner")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("Company")]
        public async Task<IResult> DeleteAsync(int id, CancellationToken ct = default)
        {
            await _companyDal.DeleteAsync(id, ct);
            return new SuccessResult(Messages.CompanyDeleted);
        }
        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        [CacheAspect(7200)] // 2 saat cache - Owner admin paneli için
        public async Task<IDataResult<List<ActiveCompanyDetailDto>>> GetActiveCompaniesAsync(CancellationToken ct = default)
        {
            var list = await _companyDal.GetActiveCompaniesAsync(ct);
            list = list.OrderBy(x => x.CompanyName).ThenBy(x => x.CompanyID).ToList();
            return new SuccessDataResult<List<ActiveCompanyDetailDto>>(list);
        }
        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        [CacheAspect(7200)] // 2 saat cache - Owner admin paneli için
        public async Task<IDataResult<List<Company>>> GetAllAsync(CancellationToken ct = default)
        {
            var list = await _companyDal.GetAllAsync(ct: ct);
            list = list.OrderBy(x => x.CompanyName).ThenBy(x => x.CompanyID).ToList();
            return new SuccessDataResult<List<Company>>(list);
        }

        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        [CacheAspect(7200)] // 2 saat cache - Owner admin paneli için
        public async Task<IDataResult<Company>> GetByIdAsync(int id, CancellationToken ct = default)
        {
            var company = await _companyDal.GetAsync(c => c.CompanyID == id, ct);
            if (company == null)
            {
                return new ErrorDataResult<Company>(Messages.CompanyNotFound);
            }
            return new SuccessDataResult<Company>(company);
        }
        [SecuredOperation("owner")]
        [ValidationAspect(typeof(CompanyValidator))]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("Company")]
        public async Task<IResult> UpdateAsync(Company company, CancellationToken ct = default)
        {
            await _companyDal.UpdateAsync(company, ct);
            return new SuccessResult(Messages.CompanyUpdated);
        }
    }
}
