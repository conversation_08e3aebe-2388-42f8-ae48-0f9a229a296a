using Core.Utilities.Results;
using Entities.DTOs;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;


namespace Business.Abstract
{
    public interface IMemberWorkoutProgramService
    {
        /// <summary>
        /// Üyeye program atar
        /// </summary>
        Task<IResult> AssignProgramAsync(MemberWorkoutProgramAddDto assignmentDto, CancellationToken ct = default);

        /// <summary>
        /// Program atamasını günceller
        /// </summary>
        Task<IResult> UpdateAssignmentAsync(MemberWorkoutProgramUpdateDto assignmentDto, CancellationToken ct = default);

        /// <summary>
        /// Program atamasını siler (soft delete)
        /// </summary>
        Task<IResult> DeleteAssignmentAsync(int assignmentId, CancellationToken ct = default);

        /// <summary>
        /// Şirket bazlı tüm program atamalarını getirir
        /// </summary>
        Task<IDataResult<List<MemberWorkoutProgramListDto>>> GetCompanyAssignmentsAsync(CancellationToken ct = default);

        /// <summary>
        /// Belirli üyenin aktif programlarını getirir
        /// </summary>
        Task<IDataResult<List<MemberWorkoutProgramDto>>> GetMemberActiveProgramsAsync(int memberId, CancellationToken ct = default);

        /// <summary>
        /// Belirli üyenin program geçmişini getirir
        /// </summary>
        Task<IDataResult<List<MemberWorkoutProgramHistoryDto>>> GetMemberProgramHistoryAsync(int memberId, CancellationToken ct = default);

        /// <summary>
        /// User ID'ye göre aktif programları getirir (mobil API için)
        /// </summary>
        Task<IDataResult<List<MemberActiveWorkoutProgramDto>>> GetActiveWorkoutProgramsByUserIdAsync(int userId, CancellationToken ct = default);

        /// <summary>
        /// Program atama detayını getirir
        /// </summary>
        Task<IDataResult<MemberWorkoutProgramDto>> GetAssignmentDetailAsync(int assignmentId, CancellationToken ct = default);

        /// <summary>
        /// Belirli programa atanan üye sayısını getirir
        /// </summary>
        Task<IDataResult<int>> GetAssignedMemberCountAsync(int workoutProgramTemplateId, CancellationToken ct = default);

        /// <summary>
        /// Üyeye atanan program detayını getirir (mobil API için)
        /// </summary>
        Task<IDataResult<MemberWorkoutProgramDetailDto>> GetProgramDetailByUserAsync(int userId, int memberWorkoutProgramId, CancellationToken ct = default);

        /// <summary>
        /// Şirket bazlı aktif atama sayısını getirir
        /// </summary>
        Task<IDataResult<int>> GetActiveAssignmentCountAsync(CancellationToken ct = default);
    }
}
