using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Transaction;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Microsoft.AspNetCore.Http;
using System.Threading;
using System.Threading.Tasks;

namespace Business.Concrete
{
    public class ProfileManager : IProfileService
    {
        IUserDal _userDal;
        IFileService _fileService;
        IAdvancedRateLimitService _advancedRateLimitService;

        public ProfileManager(IUserDal userDal, IFileService fileService, IAdvancedRateLimitService advancedRateLimitService)
        {
            _userDal = userDal;
            _fileService = fileService;
            _advancedRateLimitService = advancedRateLimitService;
        }

        [SecuredOperation("owner,admin,member")]
        [LogAspect]
        [PerformanceAspect(3)]
        public async Task<IDataResult<string>> UploadProfileImageAsync(IFormFile file, int userId, CancellationToken ct = default)
        {
            ct.ThrowIfCancellationRequested();

            // Rate limiting kontrolü
            var rateLimitCheck = await _advancedRateLimitService.CheckProfileImageUploadAttemptAsync(userId, ct);
            if (!rateLimitCheck.Success)
            {
                return new ErrorDataResult<string>(rateLimitCheck.Message);
            }

            // Dosya yükleme (IFileService async)
            var uploadResult = await _fileService.UploadProfileImageAsync(file, userId, ct);
            if (!uploadResult.Success)
            {
                return uploadResult;
            }

            // Profil image path güncelle
            var updateResult = await _userDal.UpdateProfileImagePathAsync(userId, uploadResult.Data, ct);
            if (!updateResult.Success)
            {
                return new ErrorDataResult<string>(updateResult.Message);
            }

            // Başarılı upload kaydı (rate limit sayacı)
            await _advancedRateLimitService.RecordProfileImageUploadAsync(userId, ct);

            return new SuccessDataResult<string>(uploadResult.Data, "Profil fotoğrafı başarıyla yüklendi.");
        }

        [SecuredOperation("owner,admin,member")]
        [LogAspect]
        [PerformanceAspect(3)]
        public async Task<IResult> DeleteProfileImageAsync(int userId, CancellationToken ct = default)
        {
            ct.ThrowIfCancellationRequested();

            var deleteResult = await _fileService.DeleteProfileImageAsync(userId, ct);
            if (!deleteResult.Success)
            {
                return deleteResult;
            }

            var clearResult = await _userDal.ClearProfileImagePathAsync(userId, ct);
            if (!clearResult.Success)
            {
                return clearResult;
            }

            return new SuccessResult("Profil fotoğrafı başarıyla silindi.");
        }

        [SecuredOperation("owner,admin,member")]
        [LogAspect]
        [PerformanceAspect(3)]
        public async Task<IResult> UpdateProfileImagePathAsync(int userId, string imagePath, CancellationToken ct = default)
        {
            ct.ThrowIfCancellationRequested();
            return await _userDal.UpdateProfileImagePathAsync(userId, imagePath, ct);
        }
    }
}
