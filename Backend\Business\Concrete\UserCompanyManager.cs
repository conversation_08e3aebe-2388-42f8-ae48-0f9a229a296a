﻿using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Business.ValidationRules.FluentValidation;
using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Validation;
using Core.Utilities.Results;
using DataAccess.Abstract;
using DataAccess.Concrete.EntityFramework;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Threading;


namespace Business.Concrete
{
    public class UserCompanyManager : IUserCompanyService
    {
        IUserCompanyDal _userCompanyDal;

        public UserCompanyManager(IUserCompanyDal userCompanyDal)
        {
            _userCompanyDal = userCompanyDal;
        }


        // ASYNC versiyonlar (kademeli ge<PERSON>)
        [SecuredOperation("owner")]
        [ValidationAspect(typeof(UserCompanyValidator))]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("UserCompany")]
        public async Task<IResult> AddAsync(UserCompany userCompany, CancellationToken ct = default)
        {
            var result = await _userCompanyDal.AddUserCompanyWithValidationAsync(userCompany, ct);
            return result;
        }

        [SecuredOperation("owner")]
        [LogAspect]
        [SmartCacheRemoveAspectAsync("UserCompany")]
        public async Task<IResult> DeleteAsync(int id, CancellationToken ct = default)
        {
            await _userCompanyDal.DeleteAsync(id, ct);
            return new SuccessResult(Messages.UserCompanyDeleted);
        }

        [SecuredOperation("owner")]
        [CacheAspect(3600)]
        public async Task<IDataResult<List<UserCompany>>> GetAllAsync(CancellationToken ct = default)
        {
            var list = await _userCompanyDal.GetAllAsync(ct: ct);
            return new SuccessDataResult<List<UserCompany>>(list);
        }

        [SecuredOperation("owner")]
        [ValidationAspect(typeof(UserCompanyValidator))]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("UserCompany")]
        public async Task<IResult> UpdateAsync(UserCompany userCompany, CancellationToken ct = default)
        {
            await _userCompanyDal.UpdateAsync(userCompany, ct);
            return new SuccessResult(Messages.UserCompanyUpdated);
        }

        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        [CacheAspect(3600)]
        public async Task<IDataResult<List<UserCompanyDetailDto>>> GetUserCompanyDetailsAsync(CancellationToken ct = default)
        {
            var list = await _userCompanyDal.GetUserCompanyDetailsAsync(ct);
            return new SuccessDataResult<List<UserCompanyDetailDto>>(list);
        }

        [PerformanceAspect(3)]
        public async Task<IDataResult<int>> GetUserCompanyIdAsync(int userId, CancellationToken ct = default)
        {
            int companyId = await _userCompanyDal.GetUserCompanyIdAsync(userId, ct);
            return new SuccessDataResult<int>(companyId);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(1800)]
        public async Task<IDataResult<List<UserCompany>>> GetUserCompaniesAsync(int userId, CancellationToken ct = default)
        {
            var userCompanies = await _userCompanyDal.GetActiveUserCompaniesAsync(userId, ct);
            if (userCompanies == null || userCompanies.Count == 0)
            {
                return new ErrorDataResult<List<UserCompany>>(Messages.UserCompanyNotFound);
            }
            return new SuccessDataResult<List<UserCompany>>(userCompanies);
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("UserCompany")]
        public async Task<IResult> UpdateActiveCompanyAsync(int userId, int companyId, CancellationToken ct = default)
        {
            return await _userCompanyDal.UpdateActiveCompanyWithValidationAsync(userId, companyId, ct);
        }



    }
}
