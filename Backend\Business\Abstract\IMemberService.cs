﻿using Core.Utilities.Paging;
using Core.Utilities.Results;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using MemberFilter = Entities.DTOs.MemberFilter;

namespace Business.Abstract
{
    public interface IMemberService
    {
        // Prompt 3 — Async imzalar (CT'li)
        Task<IDataResult<MemberDetailWithHistoryDto>> GetMemberDetailByIdAsync(int memberId, CancellationToken ct = default);
        Task<IDataResult<GetMemberQRByPhoneNumberDto>> GetMemberQRByPhoneNumberAsync(string phoneNumber, CancellationToken ct = default);
        Task<IDataResult<GetMemberQRByPhoneNumberDto>> GetMemberQRByUserIdWithoutCompanyFilterAsync(int userId, CancellationToken ct = default);
        Task<IDataResult<MemberProfileDto>> GetMemberProfileByUserIdAsync(int userId, CancellationToken ct = default);
        Task<IDataResult<List<Member>>> GetByMemberIdAsync(int memberid, CancellationToken ct = default);


        Task<IDataResult<List<Member>>> GetAllAsync(CancellationToken ct = default);

        // CUD Async imzalar (CT'li)
        Task<IResult> AddAsync(Member member, CancellationToken ct = default);
        Task<IResult> AddWithCardAsync(Member member, CancellationToken ct = default);
        Task<IResult> UpdateAsync(Member member, CancellationToken ct = default);
        Task<IResult> DeleteAsync(int id, CancellationToken ct = default);



        // Prompt 4 — Async imzalar (CT'li)
        Task<IDataResult<List<MembeFilterDto>>> GetMemberDetailsAsync(CancellationToken ct = default);
        Task<IDataResult<List<MemberEntryExitHistoryDto>>> GetMemberEntryExitHistoryAsync(CancellationToken ct = default);
        Task<IDataResult<List<MemberRemainingDayDto>>> GetMemberRemainingDayAsync(CancellationToken ct = default);
        Task<IDataResult<List<MemberEntryDto>>> GetMemberEntriesBySearchAsync(string searchText, CancellationToken ct = default);
        Task<IDataResult<PaginatedResult<MemberEntryDto>>> GetMemberEntriesBySearchPaginatedAsync(MemberEntryPagingParameters parameters, CancellationToken ct = default);

        // Async imzalar (CT'li)
        Task<IDataResult<MemberDetailDto>> GetMemberRemainingDaysForScanNumberAsync(string scanNumber, CancellationToken ct = default);

        // Async imzalar (CT'li)
        Task<IDataResult<List<MemberEntryDto>>> GetTodayEntriesAsync(DateTime date, CancellationToken ct = default);
        Task<IDataResult<PaginatedResult<MemberEntryDto>>> GetTodayEntriesPaginatedAsync(MemberEntryPagingParameters parameters, CancellationToken ct = default);



        // Prompt 2 — Dashboard/Metrics Async (CT'li)
        Task<IDataResult<int>> GetTotalActiveMembersAsync(CancellationToken ct = default);
        Task<IDataResult<int>> GetTotalRegisteredMembersAsync(CancellationToken ct = default);
        Task<IDataResult<Dictionary<string, int>>> GetActiveMemberCountsAsync(CancellationToken ct = default);
        Task<IDataResult<Dictionary<string, int>>> GetBranchCountsAsync(CancellationToken ct = default);
        Task<IDataResult<List<MemberBirthdayDto>>> GetUpcomingBirthdaysAsync(int days, CancellationToken ct = default);

        // Yeni async yüzeyler (CT'li) — Sync yüzeyler korunur
        Task<IDataResult<PaginatedResult<Member>>> GetAllPaginatedAsync(MemberPagingParameters parameters, CancellationToken ct = default);
        Task<IDataResult<PaginatedResult<MemberFilter>>> GetMemberDetailsPaginatedAsync(MemberPagingParameters parameters, CancellationToken ct = default);
        Task<IDataResult<PaginatedResult<MemberFilter>>> GetMembersByMultiplePackagesAsync(MemberPagingParameters parameters, CancellationToken ct = default);
        Task<IDataResult<PaginatedResult<Member>>> GetMembersWithBalancePaginatedAsync(MemberPagingParameters parameters, string balanceFilter, CancellationToken ct = default);
        Task<IDataResult<List<GetActiveMemberDto>>> GetActiveMembersAsync(CancellationToken ct = default);

        Task<IDataResult<Member>> GetMemberByUserIdAsync(int userId, CancellationToken ct = default);

        // Üye profil yönetimi metodları
        Task<IResult> UpdateMemberProfileAsync(int userId, MemberProfileUpdateDto profileUpdateDto, CancellationToken ct = default);
    }
}
