using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Transaction;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.DTOs;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Business.ValidationRules.FluentValidation;
using Core.Aspects.Autofac.Validation;


namespace Business.Concrete
{
    public class ExerciseCategoryManager : IExerciseCategoryService
    {
        readonly IExerciseCategoryDal _exerciseCategoryDal;

        public ExerciseCategoryManager(IExerciseCategoryDal exerciseCategoryDal)
        {
            _exerciseCategoryDal = exerciseCategoryDal;
        }







        // Async implementasyonlar
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(14400)]
        public async Task<IDataResult<List<ExerciseCategoryDto>>> GetAllCategoriesAsync(CancellationToken ct = default)
        {
            var list = await _exerciseCategoryDal.GetAllCategoriesAsync(ct);
            return new SuccessDataResult<List<ExerciseCategoryDto>>(list);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(14400)]
        public async Task<IDataResult<List<ExerciseCategoryDto>>> GetActiveCategoriesAsync(CancellationToken ct = default)
        {
            var list = await _exerciseCategoryDal.GetActiveCategoriesAsync(ct);
            return new SuccessDataResult<List<ExerciseCategoryDto>>(list);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(14400)]
        public async Task<IDataResult<ExerciseCategoryDto>> GetByIdAsync(int categoryId, CancellationToken ct = default)
        {
            var dto = await _exerciseCategoryDal.GetCategoryByIdAsync(categoryId, ct);
            if (dto == null)
            {
                return new ErrorDataResult<ExerciseCategoryDto>("Egzersiz kategorisi bulunamadı.");
            }
            return new SuccessDataResult<ExerciseCategoryDto>(dto);
        }

        [SecuredOperation("owner")]
        [ValidationAspect(typeof(ExerciseCategoryAddValidator))]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("ExerciseCategory")]
        public async Task<IResult> AddAsync(ExerciseCategoryAddDto categoryAddDto, CancellationToken ct = default)
        {
            return await _exerciseCategoryDal.AddExerciseCategoryAsync(categoryAddDto, ct);
        }

        [SecuredOperation("owner")]
        [ValidationAspect(typeof(ExerciseCategoryUpdateValidator))]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("ExerciseCategory")]
        public async Task<IResult> UpdateAsync(ExerciseCategoryUpdateDto categoryUpdateDto, CancellationToken ct = default)
        {
            return await _exerciseCategoryDal.UpdateExerciseCategoryAsync(categoryUpdateDto, ct);
        }

        [SecuredOperation("owner")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("ExerciseCategory")]
        public async Task<IResult> DeleteAsync(int categoryId, CancellationToken ct = default)
        {
            return await _exerciseCategoryDal.DeleteExerciseCategoryAsync(categoryId, ct);
        }
    }
}
