﻿using Core.Utilities.Results;
using Core.Utilities.Paging;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Business.Abstract
{
    public interface IMembershipTypeService
    {
        // Async yüzeyler (sync kaldırıldı)
        Task<IDataResult<List<MembershipType>>> GetAllAsync(CancellationToken ct = default);
        Task<IDataResult<PaginatedResult<MembershipType>>> GetAllPaginatedAsync(MembershipTypePagingParameters parameters, CancellationToken ct = default);
        Task<IResult> AddAsync(MembershipType membershipType, CancellationToken ct = default);
        Task<IResult> UpdateAsync(MembershipType membershipType, CancellationToken ct = default);
        Task<IResult> DeleteAsync(int id, CancellationToken ct = default);
        Task<IDataResult<List<BranchGetAllDto>>> GetBranchesAndTypesAsync(CancellationToken ct = default);
        Task<IDataResult<List<PackageWithCountDto>>> GetPackagesByBranchAsync(string branch, CancellationToken ct = default);

    }
}
