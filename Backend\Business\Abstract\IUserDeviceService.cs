﻿using Core.Entities.Concrete;
using Core.Utilities.Results;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Business.Abstract
{
    public interface IUserDeviceService
    {

        // ASYNC imzalar (CT ile)
        Task<IDataResult<List<UserDevice>>> GetActiveDevicesByUserIdAsync(int userId, CancellationToken ct = default);
        Task<IResult> AddAsync(UserDevice device, CancellationToken ct = default);
        Task<IResult> UpdateAsync(UserDevice device, CancellationToken ct = default);
        Task<IResult> DeleteAsync(int id, CancellationToken ct = default);
        Task<IDataResult<UserDevice>> GetByRefreshTokenAsync(string refreshToken, CancellationToken ct = default);
        Task<IResult> RevokeDeviceAsync(int deviceId, CancellationToken ct = default);
        Task<IResult> RevokeAllDevicesExceptCurrentAsync(int userId, string currentRefreshToken, CancellationToken ct = default);
    }
}
