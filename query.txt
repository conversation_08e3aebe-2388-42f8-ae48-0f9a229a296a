🎯 N+1 ODAKLI DENETİM – SIRA TABANLI UYGULAMA PLANI (Sadece N+1 düzelt)
======================================================================

PROJE: GymKod Pro – Multi-tenant Spor Salonu Yönetim Sistemi
AMAÇ: Tüm DAL sınıflarındaki mevcut metotların davranışını bozmadan, SADECE N+1 sorgu problemleri varsa tespit edip düzeltmek.

KAPSAM DIŞI (Açıkça yapma):
- base sınıfını değiştirme, metod imzası/DTO/sıralama/filtre mantığını değiştirme YOK.
- Gereksiz geniş Include ile veri şişirmesi YOK.
- Aynı scope’ta SaveChanges sayısını değiştirme amacı YOK (yalnızca N+1 odaklıyız).

TEMEL KURALLAR:
- Davranış Korunumu: Dönüş tipi, DTO, alan <PERSON>, kayıt sayısı, sıralama ve mevcut filtreler bire bir aynı kalacak.
- Yalnızca N+1 Fix: Varsa N+1’i projection veya gerekli Include/ThenInclude ile çözüp sorgu sayısını minimize et (genelde 1–3).
- Minimal Müdahale: En küçük değişiklikle çöz. Yeni public API/endpoint/parametre ekleme.
- Performans: AsNoTracking (read-only listelerde), projection (Select yeni DTO’ya), paging varsa korunur.
- SOLID ve mevcut mimari pattern korunur.
    17.1- Regex Kullanımı: Kod tarama ve değişikliklerde regex kullanılmayacak. Her dosya ve metot tek tek okunacak; düzenlemeler sınırlı ve hedefli yapılacak.


N+1 NASIL TESPİT EDİLİR?
- Koleksiyon/foreach içinde her kayıtta ayrı sorgu (Count/First/Where vs.).
- Navigation property erişimi serialization sırasında ek sorgular üretmesi.
- Bir listeden ID’ler alınıp her ID için ayrı sorgu çalışması.

N+1 NASIL DÜZELTİLİR?
- Projection: Gerekli alanları tek sorguda Select ile DTO’ya projekte et (tercihli çözüm).
- Include/ThenInclude: Navigation erişimi zorunluysa sadece gerekli navigation’ları Include et.
- Batch erişim: ID listesiyle tek sorguda join/alt-sorgu; gereksiz join yapma.
- Sorgu düzeni aynı kalmalı; sadece içeriği tek round-trip’e indir.

DOĞRULAMA (Hızlı Kontrol):
- Sorgu sayısı: Öncesi/Sonrası (hedef ≤ 5; çoğu listede 1–3)
- Doğruluk: Kayıt sayısı/alanları/sırası bire bir aynı.
- Varsayılan filtreler ve parametre davranışı değişmedi.

RAPOR FORMAT (Her dosya ve metot için kısa not):
- Dosya/Metot: XxxDal.cs → Yyy
- Tespit: (N+1 var/yok) + kısa kanıt (satır aralığı/kalıp)
- Çözüm: Projection/Include vb. ve 1 cümle açıklama
- Sonuç: Sorgu sayısı Önce → Sonra

İZLENECEK SIRA (A’dan Z’ye – tamamlanmadan sonraki dosyaya geçme):
1. EfCityDal.cs
2. EfCompanyAdressDal.cs
3. EfCompanyDal.cs
4. EfCompanyExerciseDal.cs
5. EfCompanyUserDal.cs
6. EfDebtPaymentDal.cs
7. EfEntryExitHistoryDal.cs
8. EfExerciseCategoryDal.cs
9. EfExpenseDal.cs
10. EfLicensePackageDal.cs
11. EfLicenseTransactionDal.cs
12. EfMemberDal.cs
13. EfMemberWorkoutProgramDal.cs
14. EfMembershipDal.cs
15. EfMembershipFreezeHistoryDal.cs
16. EfMembershipTypeDal.cs
17. EfOperationClaimDal.cs
18. EfPaymentDal.cs
19. EfProductDal.cs
20. EfRemainingDebtDal.cs
21. EfSystemExerciseDal.cs
22. EfTownDal.cs
23. EfTransactionDal.cs
24. EfUnifiedCompanyDal.cs
25. EfUserCompanyDal.cs
26. EfUserDal.cs
27. EfUserDeviceDal.cs
28. EfUserLicenseDal.cs
29. EfUserOperationClaimDal.cs
30. EfWorkoutProgramExerciseDal.cs
31. EfWorkoutProgramTemplateDal.cs

UYGULAMA ADIMI (Her dosyada tekrar edilerek):
1) Tüm public metotları sırayla oku.
2) N+1 adayı kalıpları ara (foreach içinde sorgu, navigation erişimi vb.).
3) Sorun VARSA: yalnızca N+1’i projection/Include/batch ile minimal şekilde düzelt; filtre/DTO/sıralama korunur.
4) Sorun YOKSA: doğrudan bir sonraki metoda geç; dosya bittiğinde listedeki sıradaki DAL’a otomatik geç.
5) Sorun VARSA: refactor sonrası RAPOR FORMAT’ına göre kısa özetini ilet ve sıradaki DAL’a geçiş için ONAY BEKLE.

