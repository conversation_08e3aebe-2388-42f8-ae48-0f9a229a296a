﻿using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Business.ValidationRules.FluentValidation;
using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Transaction;
using Core.Aspects.Autofac.Validation;
using Core.Utilities.Paging;
using Core.Utilities.Results;
using Core.Utilities.Security.CompanyContext;
using DataAccess.Abstract;
using DataAccess.Concrete.EntityFramework;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;

using System.Threading.Tasks;

namespace Business.Concrete
{
    public class PaymentManager : IPaymentService
    {
        IPaymentDal _paymentDal;
        private readonly ICompanyContext _companyContext;

        public PaymentManager(IPaymentDal paymentDal, ICompanyContext companyContext)
        {
            _paymentDal = paymentDal;
            _companyContext = companyContext;
        }
        // ASYNC SERVISLER
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(900)]
        public async Task<IDataResult<List<Payment>>> GetAllAsync(CancellationToken ct = default)
        {
            var list = await _paymentDal.GetAllAsync(null, ct);
            return new SuccessDataResult<List<Payment>>(list);
        }

        [SecuredOperation("owner,admin")]
        [ValidationAspect(typeof(PaymentValidator))]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("Payment")]
        public async Task<IResult> AddAsync(Payment payment, CancellationToken ct = default)
        {
            await _paymentDal.AddAsync(payment, ct);
            return new SuccessResult(Messages.PaymentAdded);
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("Payment")]
        public async Task<IResult> DeleteAsync(int id, CancellationToken ct = default)
        {
            var result = await _paymentDal.SoftDeletePaymentAsync(id, _companyContext.GetCompanyId(), ct);
            return result;
        }

        [SecuredOperation("owner,admin")]
        [ValidationAspect(typeof(PaymentValidator))]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("Payment")]
        public async Task<IResult> UpdateAsync(Payment payment, CancellationToken ct = default)
        {
            var result = await _paymentDal.UpdatePaymentWithBusinessLogicAsync(payment, _companyContext.GetCompanyId(), ct);
            return result;
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(600)]
        public async Task<IDataResult<List<PaymentHistoryDto>>> GetPaymentHistoryAsync(CancellationToken ct = default)
        {
            var result = await _paymentDal.GetPaymentHistoryAsync(ct);
            return new SuccessDataResult<List<PaymentHistoryDto>>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(300)]
        public async Task<IDataResult<List<PaymentHistoryDto>>> GetDebtorMembersAsync(CancellationToken ct = default)
        {
            var result = await _paymentDal.GetDebtorMembersAsync(ct);
            return new SuccessDataResult<List<PaymentHistoryDto>>(result);
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("Payment")]
        public async Task<IResult> UpdatePaymentStatusAsync(int paymentId, string paymentMethod, CancellationToken ct = default)
        {
            var ok = await _paymentDal.UpdatePaymentStatusAsync(paymentId, paymentMethod, ct);
            return ok ? new SuccessResult("Ödeme durumu güncellendi") : new ErrorResult("Ödeme bulunamadı");
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(600)]
        public async Task<IDataResult<PaginatedResult<PaymentHistoryDto>>> GetPaymentHistoryPaginatedAsync(PaymentPagingParameters parameters, CancellationToken ct = default)
        {
            var result = await _paymentDal.GetPaymentHistoryPaginatedAsync(parameters, ct);
            return new SuccessDataResult<PaginatedResult<PaymentHistoryDto>>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(300)]
        public async Task<IDataResult<PaymentTotals>> GetPaymentTotalsAsync(PaymentPagingParameters parameters, CancellationToken ct = default)
        {
            var result = await _paymentDal.GetPaymentTotalsAsync(parameters, ct);
            return new SuccessDataResult<PaymentTotals>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public async Task<IDataResult<MonthlyRevenueDto>> GetMonthlyRevenueAsync(int year, CancellationToken ct = default)
        {
            var result = await _paymentDal.GetMonthlyRevenueAsync(year, ct);
            return new SuccessDataResult<MonthlyRevenueDto>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(600)]
        public async Task<IDataResult<List<PaymentHistoryDto>>> GetAllPaymentHistoryFilteredAsync(PaymentPagingParameters parameters, CancellationToken ct = default)
        {
            var result = await _paymentDal.GetAllCombinedPaymentHistoryAsync(parameters, ct);
            return new SuccessDataResult<List<PaymentHistoryDto>>(result);
        }

    }
}
