﻿using Core.DataAccess;
using Core.Utilities.Results;
using Entities.Concrete;
using Entities.DTOs;
using System.Threading;
using System.Threading.Tasks;

namespace DataAccess.Abstract
{
    public interface IUserLicenseDal : IEntityRepository<UserLicense>
    {

        // Async imzalar (sync yüzey korunur)
        Task<List<UserLicenseDto>> GetUserLicenseDetailsAsync(CancellationToken ct = default);
        Task<PaginatedUserLicenseDto> GetUserLicenseDetailsPaginatedAsync(int page, int pageSize, string searchTerm, string sortBy, string companyName, int? remainingDaysMin, int? remainingDaysMax, CancellationToken ct = default);
        Task<PaginatedUserLicenseDto> GetExpiredAndPassiveLicensesAsync(int page, int pageSize, string searchTerm, CancellationToken ct = default);
        Task<List<UserLicenseDto>> GetActiveUserLicensesByUserIdAsync(int userId, CancellationToken ct = default);
        Task<UserLicenseDto> GetUserLicenseDetailAsync(int userLicenseId, CancellationToken ct = default);
        Task<IResult> PurchaseLicenseAsync(LicensePurchaseDto licensePurchaseDto, CancellationToken ct = default);
        Task<IResult> ExtendLicenseByPackageAsync(LicenseExtensionByPackageDto licenseExtensionByPackageDto, CancellationToken ct = default);
        Task<List<string>> GetUserRolesAsync(int userId, CancellationToken ct = default);
        Task<IResult> RevokeLicenseWithValidationAsync(int userLicenseId, CancellationToken ct = default);
        Task<IResult> AddUserLicenseWithDateManagementAsync(UserLicense userLicense, CancellationToken ct = default);
        Task<IResult> UpdateUserLicenseWithDateManagementAsync(UserLicense userLicense, CancellationToken ct = default);
    }

}
