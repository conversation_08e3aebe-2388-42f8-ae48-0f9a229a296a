using Core.Utilities.Paging;
using Core.Utilities.Results;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Threading;


namespace Business.Abstract
{
    public interface ICompanyExerciseService
    {
        // Async imzalar
        Task<IDataResult<List<CompanyExerciseDto>>> GetCompanyExercisesAsync(CancellationToken ct = default);
        Task<IDataResult<List<CompanyExerciseDto>>> GetCompanyExercisesByCategoryAsync(int categoryId, CancellationToken ct = default);
        Task<IDataResult<PaginatedResult<CompanyExerciseDto>>> GetCompanyExercisesFilteredAsync(CompanyExerciseFilterDto filter, CancellationToken ct = default);
        Task<IDataResult<List<CompanyExerciseDto>>> SearchCompanyExercisesAsync(string searchTerm, CancellationToken ct = default);
        Task<IDataResult<CompanyExerciseDto>> GetCompanyExerciseDetailAsync(int exerciseId, CancellationToken ct = default);
        Task<IDataResult<CompanyExerciseDto>> GetByIdAsync(int exerciseId, CancellationToken ct = default);
        Task<IResult> AddAsync(CompanyExerciseAddDto exerciseAddDto, CancellationToken ct = default);
        Task<IResult> UpdateAsync(CompanyExerciseUpdateDto exerciseUpdateDto, CancellationToken ct = default);
        Task<IResult> DeleteAsync(int exerciseId, CancellationToken ct = default);

        Task<IDataResult<List<CombinedExerciseDto>>> GetCombinedExercisesAsync(CancellationToken ct = default);
        Task<IDataResult<List<CombinedExerciseDto>>> GetCombinedExercisesByCategoryAsync(int categoryId, CancellationToken ct = default);
        Task<IDataResult<PaginatedResult<CombinedExerciseDto>>> GetCombinedExercisesFilteredAsync(SystemExerciseFilterDto filter, CancellationToken ct = default);

    }
}
