﻿﻿using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;

using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Utilities.Paging;
using Core.Utilities.Results;
using Core.Utilities.Security.CompanyContext;
using DataAccess.Abstract;
using DataAccess.Concrete.EntityFramework;
using Entities.Concrete;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace Business.Concrete
{
    public class ProductManager : IProductService
    {
        IProductDal _productDal;
        private readonly ICompanyContext _companyContext;

        public ProductManager(IProductDal productDal, ICompanyContext companyContext)
        {
            _productDal = productDal;
            _companyContext = companyContext;
        }

        // ASYNC versiyonlar
        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("Product")] // Async cache invalidation
        public async Task<IResult> AddAsync(Product product, CancellationToken ct = default)
        {
            await _productDal.AddAsync(product, ct);
            return new SuccessResult(Messages.ProductAdded);
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("Product")]
        public async Task<IResult> DeleteAsync(int productId, CancellationToken ct = default)
        {
            return await _productDal.SoftDeleteProductAsync(productId, _companyContext.GetCompanyId(), ct);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(3600)]
        public async Task<IDataResult<List<Product>>> GetAllAsync(CancellationToken ct = default)
        {
            var list = await _productDal.GetAllAsync(m => m.IsActive == true, ct);
            return new SuccessDataResult<List<Product>>(list);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(1800)]
        public async Task<IDataResult<PaginatedResult<Product>>> GetAllPaginatedAsync(ProductPagingParameters parameters, CancellationToken ct = default)
        {
            var result = await _productDal.GetAllPaginatedAsync(parameters, ct);
            return new SuccessDataResult<PaginatedResult<Product>>(result, Messages.ProductsListed);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(3600)]
        public async Task<IDataResult<Product>> GetByIdAsync(int productId, CancellationToken ct = default)
        {
            return await _productDal.GetProductByIdWithValidationAsync(productId, _companyContext.GetCompanyId(), ct);
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("Product")]
        public async Task<IResult> UpdateAsync(Product product, CancellationToken ct = default)
        {
            return await _productDal.UpdateProductWithBusinessLogicAsync(product, _companyContext.GetCompanyId(), ct);
        }
    }
}
