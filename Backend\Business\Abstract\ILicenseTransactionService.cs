﻿using Core.Utilities.Results;
using Entities.Concrete;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace Business.Abstract
{
    public interface ILicenseTransactionService
    {
        // Async yüzey (CT ile)
        Task<IDataResult<List<LicenseTransaction>>> GetAllAsync(CancellationToken ct = default);
        Task<IDataResult<List<LicenseTransaction>>> GetAllFilteredAsync(int? userID, string startDate, string endDate, int page, int pageSize, CancellationToken ct = default);
        Task<IDataResult<List<LicenseTransaction>>> GetByUserIdAsync(int userId, CancellationToken ct = default);
        Task<IResult> AddAsync(LicenseTransaction licenseTransaction, CancellationToken ct = default);
        Task<IResult> DeleteAsync(int id, CancellationToken ct = default);
        Task<IDataResult<object>> GetTotalsAsync(CancellationToken ct = default);
        Task<IDataResult<object>> GetMonthlyRevenueAsync(int year, CancellationToken ct = default);
    }
}
