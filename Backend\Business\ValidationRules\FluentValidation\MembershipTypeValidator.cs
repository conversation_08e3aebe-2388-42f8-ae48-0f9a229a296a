﻿using DataAccess.Concrete.EntityFramework;
using Entities.Concrete;
using FluentValidation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.ValidationRules.FluentValidation
{
    public class MembershipTypeValidator : AbstractValidator<MembershipType>
    {
        public MembershipTypeValidator()
        {
            RuleFor(p => p.TypeName).NotEmpty().WithMessage("Üyelik türü boş bırakılamaz.");
            RuleFor(p => p.Day).NotEmpty().WithMessage("Gün sayısı boş bırakılamaz.");
            RuleFor(p => p.Day).GreaterThan(0).WithMessage("Gün bölümüne en az 1 gün tanımlamalısınız.");
            RuleFor(p => p.Price).GreaterThan(0).WithMessage("Ücret bölümüne en az 1 TL tanımlayabilirsiniz.");
        }
    }
}
