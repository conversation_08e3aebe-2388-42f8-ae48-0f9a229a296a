﻿using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Utilities.Results;
using Core.Utilities.Security.CompanyContext;
using DataAccess.Abstract;
using Entities.DTOs;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;


namespace Business.Concrete
{
    public class RemainingDebtManager : IRemainingDebtService
    {
        private readonly IRemainingDebtDal _remainingDebtDal;
        private readonly ICompanyContext _companyContext;

        public RemainingDebtManager(IRemainingDebtDal remainingDebtDal, ICompanyContext companyContext)
        {
            _remainingDebtDal = remainingDebtDal;
            _companyContext = companyContext;
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(300)]
        public async Task<IDataResult<List<RemainingDebtDetailDto>>> GetRemainingDebtDetailsAsync(CancellationToken ct = default)
        {
            var list = await _remainingDebtDal.GetRemainingDebtDetailsAsync(ct);
            return new SuccessDataResult<List<RemainingDebtDetailDto>>(list);
        }


        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("RemainingDebt")]
        public async Task<IResult> AddDebtPaymentAsync(DebtPaymentDto debtPaymentDto, CancellationToken ct = default)
        {
            return await _remainingDebtDal.AddDebtPaymentWithBusinessLogicAsync(debtPaymentDto, ct);
        }


        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("RemainingDebt")]
        public async Task<IResult> DeleteAsync(int remainingDebtId, CancellationToken ct = default)
        {
            return await _remainingDebtDal.SoftDeleteRemainingDebtAsync(remainingDebtId, _companyContext.GetCompanyId(), ct);
        }
    }
}