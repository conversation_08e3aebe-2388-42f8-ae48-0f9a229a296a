﻿using Castle.DynamicProxy;
using Core.CrossCuttingConcerns.Validation;
using Core.Utilities.Interceptors;
using FluentValidation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Aspects.Autofac.Validation
{
    /// <summary>
    /// Async-aware validation aspect
    /// FluentValidation async kurallarını destekler
    /// 100.000+ kullanıcı için deadlock-safe
    /// </summary>
    public class ValidationAspect : MethodInterceptionAsync
    {
        private readonly Type _validatorType;

        public ValidationAspect(Type validatorType)
        {
            if (!typeof(IValidator).IsAssignableFrom(validatorType))
            {
                throw new ArgumentException("Bu bir doğrulama sınıfı değil", nameof(validatorType));
            }

            _validatorType = validatorType;
            Priority = 2; // Güvenlikten sonra - Veri doğrulama
        }

        protected override void OnBefore(IInvocation invocation)
        {
            // Async validation - deadlock-safe with proper sync bridge
            var task = ValidateAsync(invocation, CancellationToken.None);

            // Safe sync bridge for validation aspect
            try
            {
                task.ConfigureAwait(false).GetAwaiter().GetResult();
            }
            catch (AggregateException ex) when (ex.InnerException != null)
            {
                // Unwrap AggregateException to preserve original validation exception
                throw ex.InnerException;
            }
        }



        private async Task ValidateAsync(IInvocation invocation, CancellationToken cancellationToken)
        {
            try
            {
                var validator = (IValidator)Activator.CreateInstance(_validatorType);
                var entityType = _validatorType.BaseType?.GetGenericArguments()[0];

                if (entityType == null)
                    return;

                var entities = invocation.Arguments?.Where(t => t != null && t.GetType() == entityType) ?? Enumerable.Empty<object>();

                foreach (var entity in entities)
                {
                    // Async validation - deadlock-safe
                    await ValidationTool.ValidateAsync(validator, entity, cancellationToken).ConfigureAwait(false);
                }
            }
            catch (Exception ex)
            {
                // Validation hatalarını wrap etme, direkt fırlat
                throw;
            }
        }


    }
}
