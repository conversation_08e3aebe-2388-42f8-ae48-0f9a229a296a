using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;

using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Transaction;
using Core.Aspects.Autofac.Validation;
using Core.Utilities.Paging;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Business.Concrete
{
    public class SystemExerciseManager : ISystemExerciseService
    {
        ISystemExerciseDal _systemExerciseDal;

        public SystemExerciseManager(ISystemExerciseDal systemExerciseDal)
        {
            _systemExerciseDal = systemExerciseDal;
        }

        // ASYNC versiyonlar
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(14400)]
        public async Task<IDataResult<List<SystemExerciseDto>>> GetAllSystemExercisesAsync(CancellationToken ct = default)
        {
            var result = await _systemExerciseDal.GetAllSystemExercisesAsync(ct);
            return new SuccessDataResult<List<SystemExerciseDto>>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(14400)]
        public async Task<IDataResult<List<SystemExerciseDto>>> GetSystemExercisesByCategoryAsync(int categoryId, CancellationToken ct = default)
        {
            var result = await _systemExerciseDal.GetSystemExercisesByCategoryAsync(categoryId, ct);
            return new SuccessDataResult<List<SystemExerciseDto>>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(7200)]
        public async Task<IDataResult<PaginatedResult<SystemExerciseDto>>> GetSystemExercisesFilteredAsync(SystemExerciseFilterDto filter, CancellationToken ct = default)
        {
            var result = await _systemExerciseDal.GetSystemExercisesFilteredAsync(filter, ct);
            return new SuccessDataResult<PaginatedResult<SystemExerciseDto>>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(7200)]
        public async Task<IDataResult<List<SystemExerciseDto>>> SearchSystemExercisesAsync(string searchTerm, CancellationToken ct = default)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
            {
                return new ErrorDataResult<List<SystemExerciseDto>>("Arama terimi boş olamaz.");
            }
            var result = await _systemExerciseDal.SearchSystemExercisesAsync(searchTerm, ct);
            return new SuccessDataResult<List<SystemExerciseDto>>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(14400)]
        public async Task<IDataResult<SystemExerciseDto>> GetSystemExerciseDetailAsync(int exerciseId, CancellationToken ct = default)
        {
            var result = await _systemExerciseDal.GetSystemExerciseDetailAsync(exerciseId, ct);
            if (result == null)
            {
                return new ErrorDataResult<SystemExerciseDto>("Sistem egzersizi bulunamadı.");
            }
            return new SuccessDataResult<SystemExerciseDto>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(14400)]
        public async Task<IDataResult<SystemExerciseDto>> GetByIdAsync(int exerciseId, CancellationToken ct = default)
        {
            var result = await _systemExerciseDal.GetSystemExerciseDetailAsync(exerciseId, ct);
            if (result == null)
            {
                return new ErrorDataResult<SystemExerciseDto>("Sistem egzersizi bulunamadı.");
            }
            return new SuccessDataResult<SystemExerciseDto>(result);
        }

        [SecuredOperation("owner")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("SystemExercise")]
        public async Task<IResult> AddAsync(SystemExerciseAddDto exerciseAddDto, CancellationToken ct = default)
        {
            return await _systemExerciseDal.AddSystemExerciseWithManagementAsync(exerciseAddDto, ct);
        }

        [SecuredOperation("owner")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("SystemExercise")]
        public async Task<IResult> UpdateAsync(SystemExerciseUpdateDto exerciseUpdateDto, CancellationToken ct = default)
        {
            return await _systemExerciseDal.UpdateSystemExerciseWithManagementAsync(exerciseUpdateDto, ct);
        }

        [SecuredOperation("owner")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("SystemExercise")]
        public async Task<IResult> DeleteAsync(int exerciseId, CancellationToken ct = default)
        {
            return await _systemExerciseDal.SoftDeleteSystemExerciseWithValidationAsync(exerciseId, ct);
        }
    }
}
