using Core.Utilities.Paging;
using Core.Utilities.Results;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Business.Abstract
{
    public interface ISystemExerciseService
    {
        // Async yüzey
        Task<IDataResult<List<SystemExerciseDto>>> GetAllSystemExercisesAsync(CancellationToken ct = default);
        Task<IDataResult<List<SystemExerciseDto>>> GetSystemExercisesByCategoryAsync(int categoryId, CancellationToken ct = default);
        Task<IDataResult<PaginatedResult<SystemExerciseDto>>> GetSystemExercisesFilteredAsync(SystemExerciseFilterDto filter, CancellationToken ct = default);
        Task<IDataResult<List<SystemExerciseDto>>> SearchSystemExercisesAsync(string searchTerm, CancellationToken ct = default);
        Task<IDataResult<SystemExerciseDto>> GetSystemExerciseDetailAsync(int exerciseId, CancellationToken ct = default);
        Task<IDataResult<SystemExerciseDto>> GetByIdAsync(int exerciseId, CancellationToken ct = default);
        Task<IResult> AddAsync(SystemExerciseAddDto exerciseAddDto, CancellationToken ct = default);
        Task<IResult> UpdateAsync(SystemExerciseUpdateDto exerciseUpdateDto, CancellationToken ct = default);
        Task<IResult> DeleteAsync(int exerciseId, CancellationToken ct = default);
    }
}
