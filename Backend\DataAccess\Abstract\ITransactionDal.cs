﻿using Core.DataAccess;
using Core.Utilities.Results;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace DataAccess.Abstract
{
    public interface ITransactionDal : IEntityRepository<Transaction>
    {
        // ASYNC imzalar (yalnızca)
        Task<IResult> AddBulkTransactionWithBusinessLogicAsync(BulkTransactionDto bulkTransaction, int companyId, CancellationToken ct = default);
        Task<IResult> AddTransactionWithBusinessLogicAsync(Transaction transaction, int companyId, CancellationToken ct = default);
        Task<IResult> UpdatePaymentStatusWithBusinessLogicAsync(int transactionId, int companyId, CancellationToken ct = default);
        Task<IResult> UpdateAllPaymentStatusWithBusinessLogicAsync(int memberId, int companyId, CancellationToken ct = default);
        Task<IResult> SoftDeleteTransactionWithValidationAsync(int transactionId, int companyId, CancellationToken ct = default);

        // ASYNC read-only imzalar
        Task<List<TransactionDetailDto>> GetTransactionsWithDetailsAsync(CancellationToken ct = default);
        Task<List<TransactionDetailDto>> GetUnpaidTransactionsByMemberIdAsync(int memberId, CancellationToken ct = default);
        Task<decimal> GetMonthlyTransactionTotalAsync(int year, int month, CancellationToken ct = default);
        Task<decimal> GetDailyTransactionTotalAsync(DateTime date, CancellationToken ct = default);
    }
}
