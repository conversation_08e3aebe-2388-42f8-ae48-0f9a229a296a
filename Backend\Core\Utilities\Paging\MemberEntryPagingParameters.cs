using System;

namespace Core.Utilities.Paging
{
    // Core/Utilities/Paging/MemberEntryPagingParameters.cs
    public class MemberEntryPagingParameters : PagingParameters
    {
        public DateTime? Date { get; set; }
        public string SearchText { get; set; } = ""; // Default boş string
        public bool? IsActive { get; set; }

        public MemberEntryPagingParameters()
        {
            PageSize = 25; // Varsayılan sayfa boyutu 25
            PageNumber = 1; // Varsayılan sayfa numarası
            SearchText = ""; // Boş arama metni
            SortBy = "EntryTime"; // Varsayılan sıralama alanı
            SortDirection = "desc"; // Varsayılan sıralama yönü (en yeni önce)
        }

        /// <summary>
        /// Cache key generation için consistent string representation
        /// </summary>
        public override string ToString()
        {
            return $"PageNumber:{PageNumber}|PageSize:{PageSize}|SearchText:{SearchText ?? ""}|SortBy:{SortBy ?? ""}|SortDirection:{SortDirection ?? ""}|Date:{Date?.ToString("yyyy-MM-dd") ?? "null"}|IsActive:{IsActive?.ToString() ?? "null"}";
        }
    }
}
