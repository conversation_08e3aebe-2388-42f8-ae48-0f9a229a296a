﻿﻿using Business.Abstract;
using Business.BusinessAscpects.Autofac;

using Business.Constants;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Transaction;
using Core.Entities.Concrete;
using Core.Extensions;
using Core.Utilities.Results;
using Core.Utilities.Security.Hashing;
using Core.Utilities.Security.JWT;
using Entities.DTOs;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;

using System.Threading;
using System.Threading.Tasks;

namespace Business.Concrete
{
    public class AuthManager : IAuthService
    {
        private IUserService _userService;
        private ITokenHelper _tokenHelper;
        private IUserDeviceService _userDeviceService;
        private IHttpContextAccessor _httpContextAccessor;
        private IUserLicenseService _userLicenseService;
        private IUserCompanyService _userCompanyService;
        private IUserOperationClaimService _userOperationClaimService;
        private IOperationClaimService _operationClaimService;
        private IAdvancedRateLimitService _advancedRateLimitService;
        private const int MAX_ACTIVE_DEVICES = 5;

        public AuthManager(
            IUserService userService,
            ITokenHelper tokenHelper,
            IUserDeviceService userDeviceService,
            IHttpContextAccessor httpContextAccessor,
            IUserLicenseService userLicenseService,
            IUserCompanyService userCompanyService,
            IUserOperationClaimService userOperationClaimService,
            IOperationClaimService operationClaimService,
            IAdvancedRateLimitService advancedRateLimitService)
        {
            _userService = userService;
            _tokenHelper = tokenHelper;
            _userDeviceService = userDeviceService;
            _httpContextAccessor = httpContextAccessor;
            _userLicenseService = userLicenseService;
            _userCompanyService = userCompanyService;
            _userOperationClaimService = userOperationClaimService;
            _operationClaimService = operationClaimService;
            _advancedRateLimitService = advancedRateLimitService;
        }

        [LogAspect]
        [PerformanceAspect(3)]
        public async Task<IDataResult<User>> RegisterAsync(UserForRegisterDto userForRegisterDto, string password, CancellationToken ct = default)
        {
            var ipAddress = _httpContextAccessor.HttpContext?.Connection?.RemoteIpAddress?.ToString() ?? "unknown";

            // Register rate limiting kontrolü
            var rateLimitCheck = await _advancedRateLimitService.CheckRegisterAttemptAsync(ipAddress, ct);
            if (!rateLimitCheck.Success)
            {
                return new ErrorDataResult<User>(null, rateLimitCheck.Message);
            }

            try
            {
                byte[] passwordHash, passwordSalt;
                HashingHelper.CreatePasswordHash(password, out passwordHash, out passwordSalt);
                var user = new User
                {
                    Email = userForRegisterDto.Email,
                    FirstName = userForRegisterDto.FirstName,
                    LastName = userForRegisterDto.LastName,
                    PasswordHash = passwordHash,
                    PasswordSalt = passwordSalt,
                    IsActive = true,
                    CreationDate = DateTime.Now
                };
                await _userService.AddAsync(user, ct);

                // Başarılı kayıt sonrası rate limit sayacını güncelle
                await _advancedRateLimitService.RecordSuccessfulRegisterAsync(ipAddress, ct);

                return new SuccessDataResult<User>(user, Messages.UserRegistered);
            }
            catch
            {
                return new ErrorDataResult<User>(null, "Kayıt işlemi başarısız oldu.");
            }
        }

        [LogAspect]
        [PerformanceAspect(3)]
        public async Task<IDataResult<User>> RegisterMemberAsync(MemberForRegisterDto memberForRegisterDto, string password, CancellationToken ct = default)
        {
            // IP adresi ve zaman damgası gibi bilgileri loglama
            var ipAddress = _httpContextAccessor.HttpContext?.Connection?.RemoteIpAddress?.ToString() ?? "Unknown";
            var timestamp = DateTime.Now;

            // Log mesajı
            Console.WriteLine($"[{timestamp}] RegisterMemberAsync called: Email={memberForRegisterDto.Email}, FirstName={memberForRegisterDto.FirstName}, LastName={memberForRegisterDto.LastName}, IP: {ipAddress}");

            // Register rate limiting kontrolü
            var rateLimitCheck = await _advancedRateLimitService.CheckRegisterAttemptAsync(ipAddress, ct);
            if (!rateLimitCheck.Success)
            {
                return new ErrorDataResult<User>(null, rateLimitCheck.Message);
            }

            // Mevcut User var mı kontrol et
            var existingUser = await _userService.GetByMailAsync(memberForRegisterDto.Email, ct);

            User user;

            if (existingUser != null)
            {
                user = existingUser;
                if (user.RequirePasswordChange)
                {
                    byte[] passwordHash, passwordSalt;
                    HashingHelper.CreatePasswordHash(password, out passwordHash, out passwordSalt);
                    // Şifre ve RequirePasswordChange alanlarını dar-kapsamlı metodla güncelle
                    await _userService.UpdatePasswordAsync(user.UserID, passwordHash, passwordSalt, false, ct);
                }
            }
            else
            {
                byte[] passwordHash, passwordSalt;
                HashingHelper.CreatePasswordHash(password, out passwordHash, out passwordSalt);
                user = new User
                {
                    Email = memberForRegisterDto.Email,
                    FirstName = memberForRegisterDto.FirstName,
                    LastName = memberForRegisterDto.LastName,
                    PasswordHash = passwordHash,
                    PasswordSalt = passwordSalt,
                    IsActive = true,
                    RequirePasswordChange = false,
                    CreationDate = DateTime.Now
                };
                await _userService.AddAsync(user, ct);

                var memberRoleResult = await _operationClaimService.GetByNameAsync("member", ct);
                if (memberRoleResult.Success && memberRoleResult.Data != null)
                {
                    var userOperationClaim = new UserOperationClaim
                    {
                        UserId = user.UserID,
                        OperationClaimId = memberRoleResult.Data.OperationClaimId,
                        IsActive = true,
                        CreationDate = DateTime.Now
                    };
                    await _userOperationClaimService.AddForRegistrationAsync(userOperationClaim, ct);
                }
            }

            await _advancedRateLimitService.RecordSuccessfulRegisterAsync(ipAddress, ct);
            return new SuccessDataResult<User>(user, Messages.MemberRegistered);
        }

        [LogAspect]
        [PerformanceAspect(3)]
        public async Task<IDataResult<User>> LoginAsync(UserForLoginDto userForLoginDto, string deviceInfo, CancellationToken ct = default)
        {
            var ipAddress = _httpContextAccessor.HttpContext?.Connection?.RemoteIpAddress?.ToString() ?? "unknown";
            var userAgent = _httpContextAccessor.HttpContext?.Request?.Headers["User-Agent"].ToString() ?? "unknown";

            // Device fingerprint oluştur
            var deviceFingerprint = await _advancedRateLimitService.GenerateDeviceFingerprintAsync(ipAddress, userAgent, deviceInfo, ct);

            // Login rate limiting kontrolü - BAN durumunda işlemi tamamen durdur
            var rateLimitCheck = await _advancedRateLimitService.CheckLoginAttemptAsync(ipAddress, deviceFingerprint, ct);
            if (!rateLimitCheck.Success)
            {
                return new ErrorDataResult<User>(null, rateLimitCheck.Message);
            }

            var userToCheck = await _userService.GetByMailAsync(userForLoginDto.Email, ct);
            if (userToCheck == null)
            {
                await _advancedRateLimitService.RecordFailedLoginAsync(ipAddress, deviceFingerprint, ct);
                return new ErrorDataResult<User>(null, Messages.UserNotFound);
            }

            if (!HashingHelper.VerifyPasswordHash(userForLoginDto.Password, userToCheck.PasswordHash, userToCheck.PasswordSalt))
            {
                await _advancedRateLimitService.RecordFailedLoginAsync(ipAddress, deviceFingerprint, ct);
                return new ErrorDataResult<User>(null, Messages.PasswordError);
            }

            if (!userToCheck.IsActive)
            {
                await _advancedRateLimitService.RecordFailedLoginAsync(ipAddress, deviceFingerprint, ct);
                return new ErrorDataResult<User>(null, "Hesabınız pasif durumda. Lütfen yönetici ile iletişime geçin.");
            }

            await _advancedRateLimitService.RecordSuccessfulLoginAsync(ipAddress, deviceFingerprint, ct);

            if (userToCheck.RequirePasswordChange)
            {
                return new SuccessDataResult<User>(userToCheck, "PasswordChangeRequired");
            }

            return new SuccessDataResult<User>(userToCheck, Messages.SuccessfulLogin);
        }



        private string GetCurrentIPAddress()
        {
            return _httpContextAccessor.HttpContext?.Connection?.RemoteIpAddress?.ToString();
        }

        public async Task<IResult> UserExistsAsync(string email, CancellationToken ct = default)
        {
            if (await _userService.GetByMailAsync(email, ct) != null)
            {
                return new ErrorResult(Messages.UserAlreadyExists);
            }
            return new SuccessResult();
        }

        [SecuredOperation("owner,admin,member")]
        [LogAspect]
        [PerformanceAspect(3)]
        public async Task<IResult> ChangePasswordAsync(int userId, string currentPassword, string newPassword, CancellationToken ct = default)
        {
            var userResult = await _userService.GetByIdAsync(userId, ct);
            if (!userResult.Success)
            {
                return new ErrorResult(Messages.UserNotFound);
            }

            var user = userResult.Data;

            if (!HashingHelper.VerifyPasswordHash(currentPassword, user.PasswordHash, user.PasswordSalt))
            {
                return new ErrorResult(Messages.PasswordError);
            }

            if (newPassword.Length < 4)
            {
                return new ErrorResult("Şifre en az 4 karakter olmalıdır.");
            }

            byte[] passwordHash, passwordSalt;
            HashingHelper.CreatePasswordHash(newPassword, out passwordHash, out passwordSalt);

            // Sadece şifre alanlarını güncelleyen dar-kapsamlı metodu kullan
            var updateResult = await _userService.UpdatePasswordAsync(user.UserID, passwordHash, passwordSalt, false, ct);
            if (!updateResult.Success)
            {
                return new ErrorResult(updateResult.Message);
            }

            return new SuccessResult("Şifreniz başarıyla değiştirildi.");
        }

        [SecuredOperation("owner,admin,member")]
        [PerformanceAspect(3)]
        public async Task<IDataResult<bool>> CheckPasswordChangeRequiredAsync(int userId, CancellationToken ct = default)
        {
            var userResult = await _userService.GetByIdAsync(userId, ct);
            if (!userResult.Success)
            {
                return new ErrorDataResult<bool>(false, Messages.UserNotFound);
            }

            return new SuccessDataResult<bool>(userResult.Data.RequirePasswordChange);
        }

        [PerformanceAspect(2)]
        public async Task<IDataResult<AccessToken>> CreateAccessTokenAsync(User user, string deviceInfo, CancellationToken ct = default)
        {
            var defaultClaims = await _userService.GetClaimsAsync(user, ct);
            var licensedRoles = (await _userLicenseService.GetUserRolesAsync(user.UserID, ct)).Data;

            var allClaims = new List<OperationClaim>(defaultClaims);
            foreach (var role in licensedRoles)
            {
                if (!allClaims.Any(c => c.Name == role))
                {
                    allClaims.Add(new OperationClaim { Name = role });
                }
            }

            var companyIdResult = await _userCompanyService.GetUserCompanyIdAsync(user.UserID, ct);
            int companyId = companyIdResult.Success ? companyIdResult.Data : -1;

            var accessToken = _tokenHelper.CreateToken(user, allClaims, companyId);
            var refreshToken = _tokenHelper.CreateRefreshToken(user);

            var userDevice = new UserDevice
            {
                UserId = user.UserID,
                DeviceInfo = deviceInfo,
                RefreshToken = refreshToken.Token,
                LastIpAddress = GetCurrentIPAddress(),
                RefreshTokenExpiration = refreshToken.Expiration,
                IsActive = true,
                CreatedAt = DateTime.Now,
                LastUsedAt = DateTime.Now
            };

            await _userDeviceService.AddAsync(userDevice, ct);

            accessToken.RefreshToken = refreshToken.Token;
            return new SuccessDataResult<AccessToken>(accessToken, Messages.SuccessfulLogin);
        }

        public async Task<IDataResult<AccessToken>> CreateAccessTokenWithRefreshTokenAsync(string refreshToken, string ipAddress, string deviceInfo, CancellationToken ct = default)
        {
            var userDevice = await _userDeviceService.GetByRefreshTokenAsync(refreshToken, ct);
            if (!userDevice.Success)
            {
                return new ErrorDataResult<AccessToken>(Messages.InvalidRefreshToken);
            }

            var device = userDevice.Data;

            if (!device.IsActive)
            {
                return new ErrorDataResult<AccessToken>("DEVICE_REVOKED: Bu cihazdan oturumunuz sonlandırılmıştır. Hesabınıza başka bir cihazdan giriş yapılmış olabilir.");
            }

            if (device.RefreshTokenExpiration <= DateTime.Now)
            {
                await _userDeviceService.RevokeDeviceAsync(device.Id, ct);
                return new ErrorDataResult<AccessToken>(Messages.ExpiredRefreshToken);
            }

            var userResult = await _userService.GetByIdAsync(device.UserId, ct);
            if (!userResult.Success)
            {
                return new ErrorDataResult<AccessToken>(Messages.UserNotFound);
            }

            var defaultClaims = await _userService.GetClaimsAsync(userResult.Data, ct);
            var licensedRoles = (await _userLicenseService.GetUserRolesAsync(userResult.Data.UserID, ct)).Data;

            var allClaims = new List<OperationClaim>(defaultClaims);
            foreach (var role in licensedRoles)
            {
                if (!allClaims.Any(c => c.Name == role))
                {
                    allClaims.Add(new OperationClaim { Name = role });
                }
            }

            var companyIdResult = await _userCompanyService.GetUserCompanyIdAsync(userResult.Data.UserID, ct);
            int companyId = companyIdResult.Success ? companyIdResult.Data : -1;

            var accessToken = _tokenHelper.CreateToken(userResult.Data, allClaims, companyId);
            var newRefreshToken = _tokenHelper.CreateRefreshToken(userResult.Data);

            device.RefreshToken = newRefreshToken.Token;
            device.LastIpAddress = ipAddress;
            device.LastUsedAt = DateTime.Now;
            device.DeviceInfo = deviceInfo;

            await _userDeviceService.UpdateAsync(device, ct);

            accessToken.RefreshToken = newRefreshToken.Token;
            return new SuccessDataResult<AccessToken>(accessToken, Messages.TokensRefreshed);
        }



        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public async Task<IDataResult<AccessToken>> ChangeCompanyAsync(int userId, int companyId, string deviceInfo, CancellationToken ct = default)
        {
            var userResult = await _userService.GetByIdAsync(userId, ct);
            if (!userResult.Success)
            {
                return new ErrorDataResult<AccessToken>(Messages.UserNotFound);
            }

            var userCompanies = await _userCompanyService.GetUserCompaniesAsync(userId, ct);
            if (!userCompanies.Success)
            {
                return new ErrorDataResult<AccessToken>(Messages.UserCompanyNotFound);
            }

            if (!userCompanies.Data.Any(uc => uc.CompanyId == companyId && uc.IsActive == true))
            {
                return new ErrorDataResult<AccessToken>(Messages.UserCompanyAccessDenied);
            }

            var updateResult = await _userCompanyService.UpdateActiveCompanyAsync(userId, companyId, ct);
            if (!updateResult.Success)
            {
                return new ErrorDataResult<AccessToken>(updateResult.Message);
            }

            var defaultClaims = await _userService.GetClaimsAsync(userResult.Data, ct);
            var licensedRoles = (await _userLicenseService.GetUserRolesAsync(userId, ct)).Data;

            var allClaims = new List<OperationClaim>(defaultClaims);
            foreach (var role in licensedRoles)
            {
                if (!allClaims.Any(c => c.Name == role))
                {
                    allClaims.Add(new OperationClaim { Name = role });
                }
            }

            var accessToken = _tokenHelper.CreateToken(userResult.Data, allClaims, companyId);
            var refreshToken = _tokenHelper.CreateRefreshToken(userResult.Data);

            var userDevice = new UserDevice
            {
                UserId = userId,
                DeviceInfo = deviceInfo,
                RefreshToken = refreshToken.Token,
                LastIpAddress = GetCurrentIPAddress(),
                RefreshTokenExpiration = refreshToken.Expiration,
                IsActive = true,
                CreatedAt = DateTime.Now,
                LastUsedAt = DateTime.Now
            };

            await _userDeviceService.AddAsync(userDevice, ct);

            accessToken.RefreshToken = refreshToken.Token;
            return new SuccessDataResult<AccessToken>(accessToken, Messages.CompanyChanged);
        }

        // Rate Limiting Methods (ASYNC only - KEEP_SYNC=false)
        public async Task<string> GenerateDeviceFingerprintAsync(string ipAddress, string userAgent, string deviceInfo, CancellationToken ct = default)
        {
            return await _advancedRateLimitService.GenerateDeviceFingerprintAsync(ipAddress, userAgent, deviceInfo, ct);
        }

        public async Task<IDataResult<int>> GetRemainingLoginBanTimeAsync(string ipAddress, string deviceFingerprint, CancellationToken ct = default)
        {
            return await _advancedRateLimitService.GetRemainingLoginBanTimeAsync(ipAddress, deviceFingerprint, ct);
        }

        public async Task<IDataResult<int>> GetRemainingRegisterBanTimeAsync(string ipAddress, CancellationToken ct = default)
        {
            return await _advancedRateLimitService.GetRemainingRegisterBanTimeAsync(ipAddress, ct);
        }

        // ASYNC versiyonlar (UserDevice ile ilgili)
        [PerformanceAspect(3)]
        public async Task<IResult> RevokeRefreshTokenAsync(string refreshToken, CancellationToken ct = default)
        {
            var userDevice = await _userDeviceService.GetByRefreshTokenAsync(refreshToken, ct);
            if (!userDevice.Success)
            {
                return new ErrorResult(Messages.InvalidRefreshToken);
            }
            return await _userDeviceService.RevokeDeviceAsync(userDevice.Data.Id, ct);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public async Task<IResult> RevokeAllDevicesAsync(int userId, CancellationToken ct = default)
        {
            var currentRefreshToken = _httpContextAccessor.HttpContext?.Request?.Headers["X-Refresh-Token"].ToString();
            return await _userDeviceService.RevokeAllDevicesExceptCurrentAsync(userId, currentRefreshToken, ct);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public async Task<IResult> RevokeDeviceAsync(int deviceId, CancellationToken ct = default)
        {
            return await _userDeviceService.RevokeDeviceAsync(deviceId, ct);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public async Task<IDataResult<List<UserDeviceDto>>> GetUserDevicesAsync(int userId, CancellationToken ct = default)
        {
            var devices = await _userDeviceService.GetActiveDevicesByUserIdAsync(userId, ct);
            if (!devices.Success)
            {
                return new ErrorDataResult<List<UserDeviceDto>>(devices.Message);
            }

            var currentRefreshToken = _httpContextAccessor.HttpContext?.Request?.Headers["X-Refresh-Token"].ToString();

            var deviceDtos = devices.Data.Select(d => new UserDeviceDto
            {
                Id = d.Id,
                DeviceInfo = d.DeviceInfo,
                LastIpAddress = d.LastIpAddress,
                CreatedAt = d.CreatedAt,
                LastUsedAt = d.LastUsedAt,
                IsCurrentDevice = !string.IsNullOrEmpty(currentRefreshToken) && d.RefreshToken == currentRefreshToken
            }).OrderByDescending(d => d.IsCurrentDevice).ThenByDescending(d => d.LastUsedAt).ToList();

            return new SuccessDataResult<List<UserDeviceDto>>(deviceDtos);
        }
}


}
