﻿using Core.DataAccess;
using Core.Utilities.Paging;
using Core.Utilities.Results;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace DataAccess.Abstract
{
    public interface IPaymentDal: IEntityRepository<Payment>
    {
        // Async imzalar (CT'li)
        Task<List<PaymentHistoryDto>> GetPaymentHistoryAsync(CancellationToken ct = default);
        Task<List<PaymentHistoryDto>> GetDebtorMembersAsync(CancellationToken ct = default);
        Task<bool> UpdatePaymentStatusAsync(int paymentId, string paymentMethod, CancellationToken ct = default);
        Task<PaginatedResult<PaymentHistoryDto>> GetPaymentHistoryPaginatedAsync(PaymentPagingParameters parameters, CancellationToken ct = default);
        Task<PaymentTotals> GetPaymentTotalsAsync(PaymentPagingParameters parameters, CancellationToken ct = default);
        Task<MonthlyRevenueDto> GetMonthlyRevenueAsync(int year, CancellationToken ct = default);
        Task<List<PaymentHistoryDto>> GetAllCombinedPaymentHistoryAsync(PaymentPagingParameters parameters, CancellationToken ct = default);

        // Complex business operations (async)
        Task<IResult> SoftDeletePaymentAsync(int paymentId, int companyId, CancellationToken ct = default);
        Task<IResult> UpdatePaymentWithBusinessLogicAsync(Payment payment, int companyId, CancellationToken ct = default);
        Task<IResult> UpdatePaymentStatusWithBusinessLogicAsync(int paymentId, string paymentMethod, CancellationToken ct = default);
    }
}
