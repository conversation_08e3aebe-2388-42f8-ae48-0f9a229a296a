﻿// Core/Utilities/Security/JWT/JwtHelper.cs
using Core.Entities.Concrete;
using Core.Extensions;
using Core.Utilities.Security.Encryption;
using Core.Utilities.Security.Environment;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Security.Cryptography;

namespace Core.Utilities.Security.JWT
{
    public class JwtHelper : ITokenHelper
    {
        public IConfiguration Configuration { get; }
        private TokenOptions _tokenOptions;
        private DateTime _accessTokenExpiration;
        private DateTime _refreshTokenExpiration;

        public JwtHelper(IConfiguration configuration)
        {
            Configuration = configuration;

            // Environment anahtarını oku
            var environment = Configuration["Environment"] ?? "dev";

            // Environment'a göre TokenOptions'ı al
            _tokenOptions = Configuration.GetSection($"TokenOptions:{environment}").Get<TokenOptions>();

            if (_tokenOptions == null)
            {
                throw new InvalidOperationException($"'{environment}' environment'ı için TokenOptions bulunamadı!");
            }

            // SecurityKey'i environment helper ile güvenli şekilde al
            var secureKey = EnvironmentSecurityHelper.GetSecurityKey(environment, _tokenOptions.SecurityKey);

            // SecurityKey güvenlik kontrolü
            if (!EnvironmentSecurityHelper.ValidateSecurityKey(secureKey))
            {
                throw new InvalidOperationException($"'{environment}' environment'ı için SecurityKey güvenlik gereksinimlerini karşılamıyor!");
            }

            _tokenOptions.SecurityKey = secureKey;
        }

        public AccessToken CreateToken(User user, List<OperationClaim> operationClaims, int companyId = -1)
        {
            _accessTokenExpiration = DateTime.Now.AddMinutes(_tokenOptions.AccessTokenExpiration);
            var securityKey = SecurityKeyHelper.CreateSecurityKey(_tokenOptions.SecurityKey);
            var signingCredentials = SigningCredentialsHelper.CreateSigningCredentials(securityKey);
            var jwt = CreateJwtSecurityToken(_tokenOptions, user, signingCredentials, operationClaims, companyId);
            var jwtSecurityTokenHandler = new JwtSecurityTokenHandler();
            var token = jwtSecurityTokenHandler.WriteToken(jwt);

            return new AccessToken
            {
                Token = token,
                Expiration = _accessTokenExpiration,
                RefreshToken = "" // Boş olarak başlatıyoruz, daha sonra AuthManager'da set edilecek
            };
        }


        public RefreshToken CreateRefreshToken(User user)
        {
            _refreshTokenExpiration = DateTime.Now.AddDays(_tokenOptions.RefreshTokenExpiration);

            var randomNumber = new byte[32];
            using (var rng = RandomNumberGenerator.Create())
            {
                rng.GetBytes(randomNumber);
                return new RefreshToken
                {
                    Token = Convert.ToBase64String(randomNumber),
                    Expiration = _refreshTokenExpiration
                };
            }
        }

      

        private JwtSecurityToken CreateJwtSecurityToken(TokenOptions tokenOptions, User user,
            SigningCredentials signingCredentials, List<OperationClaim> operationClaims, int companyId = -1)
        {
            var jwt = new JwtSecurityToken(
                issuer: tokenOptions.Issuer,
                audience: tokenOptions.Audience,
                expires: _accessTokenExpiration,
                notBefore: DateTime.Now,
                claims: SetClaims(user, operationClaims, companyId),
                signingCredentials: signingCredentials
            );
            return jwt;
        }

        private IEnumerable<Claim> SetClaims(User user, List<OperationClaim> operationClaims, int companyId = -1)
        {
            var claims = new List<Claim>();
            claims.AddNameIdentifier(user.UserID.ToString());
            claims.AddEmail(user.Email);
            claims.AddName($"{user.FirstName} {user.LastName}");
            claims.AddRoles(operationClaims.Select(c => c.Name).ToArray());
            
            // Eğer geçerli bir şirket ID'si varsa, claim'e ekle
            if (companyId > 0)
            {
                claims.AddCompanyId(companyId.ToString());
            }
            
            return claims;
        }
    }
}