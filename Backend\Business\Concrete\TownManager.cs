﻿using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Performance;

using Core.Entities.Concrete;
using Core.Utilities.Results;
using DataAccess.Abstract;
using DataAccess.Concrete.EntityFramework;
using Entities.Concrete;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Business.Concrete
{
    public class TownManager : ITownService
    {
        ITownDal _townDal;

        public TownManager(ITownDal townDal)
        {
            _townDal = townDal;
        }
        // ASYNC versiyonlar
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(86400)]
        public async Task<IDataResult<List<Town>>> GetAllAsync(CancellationToken ct = default)
        {
            var list = await _townDal.GetAllAsync(ct: ct);
            list = list.OrderBy(x => x.TownName).ToList();
            return new SuccessDataResult<List<Town>>(list);
        }
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(86400)]
        public async Task<IDataResult<List<Town>>> GetByCityIdAsync(int cityId, CancellationToken ct = default)
        {
            var list = await _townDal.GetAllAsync(c => c.CityID == cityId, ct);
            list = list.OrderBy(x => x.TownName).ToList();
            return new SuccessDataResult<List<Town>>(list);
        }
    }
}
