﻿using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;

using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Transaction;
using Core.Utilities.Results;
using Core.Utilities.Security.CompanyContext;
using DataAccess.Abstract;
using DataAccess.Concrete.EntityFramework;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;


namespace Business.Concrete
{
    public class TransactionManager : ITransactionService
    {
        private readonly ITransactionDal _transactionDal;
        private readonly IMemberDal _memberDal;
        private readonly ICompanyContext _companyContext;

        public TransactionManager(ITransactionDal transactionDal, IMemberDal memberDal, ICompanyContext companyContext)
        {
            _transactionDal = transactionDal;
            _memberDal = memberDal;
            _companyContext = companyContext;
        }


        // ASYNC versiyonlar (kademeli geçiş için ekleniyor)
        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("Transaction")]
        public async Task<IResult> AddAsync(Transaction transaction, CancellationToken ct = default)
        {
            var companyId = _companyContext.GetCompanyId();
            return await _transactionDal.AddTransactionWithBusinessLogicAsync(transaction, companyId, ct);
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("Transaction")]
        public async Task<IResult> AddBulkAsync(BulkTransactionDto bulkTransaction, CancellationToken ct = default)
        {
            var companyId = _companyContext.GetCompanyId();
            return await _transactionDal.AddBulkTransactionWithBusinessLogicAsync(bulkTransaction, companyId, ct);
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("Transaction")]
        public async Task<IResult> UpdatePaymentStatusAsync(int transactionId, CancellationToken ct = default)
        {
            var companyId = _companyContext.GetCompanyId();
            return await _transactionDal.UpdatePaymentStatusWithBusinessLogicAsync(transactionId, companyId, ct);
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("Transaction")]
        public async Task<IResult> UpdateAllPaymentStatusAsync(int memberId, CancellationToken ct = default)
        {
            var companyId = _companyContext.GetCompanyId();
            return await _transactionDal.UpdateAllPaymentStatusWithBusinessLogicAsync(memberId, companyId, ct);
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("Transaction")]
        public async Task<IResult> DeleteAsync(int transactionId, CancellationToken ct = default)
        {
            var companyId = _companyContext.GetCompanyId();
            return await _transactionDal.SoftDeleteTransactionWithValidationAsync(transactionId, companyId, ct);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(900)]
        public async Task<IDataResult<List<Transaction>>> GetAllAsync(CancellationToken ct = default)
        {
            var list = await _transactionDal.GetAllAsync(null, ct);
            return new SuccessDataResult<List<Transaction>>(list);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(600)]
        public async Task<IDataResult<List<Transaction>>> GetByMemberIdAsync(int memberId, CancellationToken ct = default)
        {
            var list = await _transactionDal.GetAllAsync(t => t.MemberID == memberId, ct);
            return new SuccessDataResult<List<Transaction>>(list);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(600)]
        public async Task<IDataResult<List<TransactionDetailDto>>> GetTransactionsWithDetailsAsync(CancellationToken ct = default)
        {
            var list = await _transactionDal.GetTransactionsWithDetailsAsync(ct);
            return new SuccessDataResult<List<TransactionDetailDto>>(list);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(300)]
        public async Task<IDataResult<List<TransactionDetailDto>>> GetUnpaidTransactionsAsync(int memberId, CancellationToken ct = default)
        {
            var list = await _transactionDal.GetUnpaidTransactionsByMemberIdAsync(memberId, ct);
            return new SuccessDataResult<List<TransactionDetailDto>>(list);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(1800)]
        public async Task<IDataResult<decimal>> GetMonthlyTransactionTotalAsync(int year, int month, CancellationToken ct = default)
        {
            var total = await _transactionDal.GetMonthlyTransactionTotalAsync(year, month, ct);
            return new SuccessDataResult<decimal>(total);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(600)]
        public async Task<IDataResult<decimal>> GetDailyTransactionTotalAsync(DateTime date, CancellationToken ct = default)
        {
            var total = await _transactionDal.GetDailyTransactionTotalAsync(date, ct);
            return new SuccessDataResult<decimal>(total);
        }
    }
}