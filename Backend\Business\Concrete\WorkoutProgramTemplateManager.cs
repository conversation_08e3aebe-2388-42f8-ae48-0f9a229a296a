using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Business.ValidationRules.FluentValidation;

using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Transaction;
using Core.Aspects.Autofac.Validation;
using Core.Utilities.Results;
using Core.Utilities.Security.CompanyContext;
using DataAccess.Abstract;
using Entities.DTOs;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace Business.Concrete
{
    public class WorkoutProgramTemplateManager : IWorkoutProgramTemplateService
    {
        private readonly IWorkoutProgramTemplateDal _workoutProgramTemplateDal;
        private readonly ICompanyContext _companyContext;

        public WorkoutProgramTemplateManager(
            IWorkoutProgramTemplateDal workoutProgramTemplateDal,
            ICompanyContext companyContext)
        {
            _workoutProgramTemplateDal = workoutProgramTemplateDal;
            _companyContext = companyContext;
        }


        // Async yüzeyler
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(1800)]
        public async Task<IDataResult<List<WorkoutProgramTemplateListDto>>> GetAllAsync(CancellationToken ct = default)
        {
            var result = await _workoutProgramTemplateDal.GetWorkoutProgramTemplateListAsync(ct);
            return new SuccessDataResult<List<WorkoutProgramTemplateListDto>>(result, Messages.WorkoutProgramsListed);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(1800)]
        public async Task<IDataResult<WorkoutProgramTemplateDto>> GetByIdAsync(int templateId, CancellationToken ct = default)
        {
            var templateResult = await _workoutProgramTemplateDal.GetWorkoutProgramTemplateByIdWithValidationAsync(templateId, ct);
            if (!templateResult.Success)
            {
                return new ErrorDataResult<WorkoutProgramTemplateDto>(templateResult.Message);
            }
            var result = await _workoutProgramTemplateDal.GetWorkoutProgramTemplateDetailAsync(templateId, ct);
            if (result == null)
            {
                return new ErrorDataResult<WorkoutProgramTemplateDto>(Messages.WorkoutProgramNotFound);
            }
            return new SuccessDataResult<WorkoutProgramTemplateDto>(result, Messages.WorkoutProgramDetailRetrieved);
        }

        [SecuredOperation("owner,admin")]
        [ValidationAspect(typeof(WorkoutProgramTemplateAddValidator))]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("WorkoutProgramTemplate")]
        public async Task<IResult> AddAsync(WorkoutProgramTemplateAddDto templateAddDto, CancellationToken ct = default)
        {
            var companyId = _companyContext.GetCompanyId();
            return await _workoutProgramTemplateDal.AddWorkoutProgramWithBusinessRulesAsync(templateAddDto, companyId, ct);
        }

        [SecuredOperation("owner,admin")]
        [ValidationAspect(typeof(WorkoutProgramTemplateUpdateValidator))]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("WorkoutProgramTemplate")]
        public async Task<IResult> UpdateAsync(WorkoutProgramTemplateUpdateDto templateUpdateDto, CancellationToken ct = default)
        {
            var companyId = _companyContext.GetCompanyId();
            return await _workoutProgramTemplateDal.UpdateWorkoutProgramWithBusinessRulesAsync(templateUpdateDto, companyId, ct);
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("WorkoutProgramTemplate")]
        public async Task<IResult> DeleteAsync(int templateId, CancellationToken ct = default)
        {
            var companyId = _companyContext.GetCompanyId();
            return await _workoutProgramTemplateDal.SoftDeleteWorkoutProgramWithValidationAsync(templateId, companyId, ct);
        }

}
}
