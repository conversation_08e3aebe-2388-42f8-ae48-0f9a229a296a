﻿using Core.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Core.DataAccess
{
    public interface IEntityRepository<T> where T : class, IEntity, new()
    {
        // Async imzalar (CancellationToken ile)
        Task<List<T>> GetAllAsync(Expression<Func<T, bool>> filter = null, CancellationToken ct = default);
        Task<T> GetAsync(Expression<Func<T, bool>> filter, CancellationToken ct = default);
        Task AddAsync(T entity, CancellationToken ct = default);
        Task UpdateAsync(T entity, CancellationToken ct = default);
        Task DeleteAsync(object id, CancellationToken ct = default);
        Task HardDeleteAsync(object id, CancellationToken ct = default);
    }
}
