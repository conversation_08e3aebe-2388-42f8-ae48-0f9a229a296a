﻿using Core.Utilities.Results;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace Business.Abstract
{
    public interface IMembershipService
    {
        // Sync imzalar kaldırıldı - yalnızca async yüzeyler kullanılacak

        // Async yüzeyler (CancellationToken ile)
        Task<IDataResult<List<Membership>>> GetAllAsync(CancellationToken ct = default);
        Task<IResult> AddAsync(MembershipAddDto membership, CancellationToken ct = default);
        Task<IResult> UpdateAsync(MembershipUpdateDto membership, CancellationToken ct = default);
        Task<IResult> DeleteAsync(int id, CancellationToken ct = default);
        Task<IDataResult<List<Membership>>> GetByMembershipIdAsync(int memberid, CancellationToken ct = default);
        Task<IDataResult<LastMembershipInfoDto>> GetLastMembershipInfoAsync(int memberId, CancellationToken ct = default);
        Task<IResult> FreezeMembershipAsync(MembershipFreezeRequestDto freezeRequest, CancellationToken ct = default);
        Task<IResult> UnfreezeMembershipAsync(int membershipId, CancellationToken ct = default);
        Task<IDataResult<List<MembershipFreezeDto>>> GetFrozenMembershipsAsync(CancellationToken ct = default);
        Task<IResult> CancelFreezeAsync(int membershipId, CancellationToken ct = default);
        Task<IResult> ReactivateFromTodayAsync(int membershipId, CancellationToken ct = default);
        Task<IDataResult<List<MembershipDetailForDeleteDto>>> GetMemberActiveMembershipsAsync(int memberId, CancellationToken ct = default);

    }
}