using Business.Abstract;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Utilities.Results;
using Entities.DTOs;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;

namespace Business.Concrete
{
    public class QrCodeEncryptionManager : IQrCodeEncryptionService
    {
        // QR kod okuyucuları için güvenli karakter seti (ScanNumber ile aynı)
        private const string SafeChars = "ABCDEFGHJKLMNPQRSTUVWXYZ23456789";

        // 32 byte (256-bit) encryption key - Gerçek uygulamada bu appsettings'den alınmalı
        private static readonly string EncryptionKey = "GymProjectQREncryptionKey2024!@#$";

        [LogAspect]
        [PerformanceAspect(2)]
        public string CreateEncryptedQrToken(int memberId, string scanNumber)
        {
            try
            {
                // QR kod için veri yapı<PERSON>ı oluştur
                var qrData = new
                {
                    MemberId = memberId,
                    ScanNumber = scanNumber,
                    Timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                    CreatedAt = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
                };

                // JSON'a çevir
                string jsonData = JsonSerializer.Serialize(qrData);

                // AES ile şifrele
                string encryptedData = EncryptAES(jsonData);

                return encryptedData;
            }
            catch (Exception ex)
            {
                // Log the error but don't expose details
                return string.Empty;
            }
        }

        [LogAspect]
        [PerformanceAspect(2)]
        public IDataResult<DecryptedQrCodeDto> DecryptQrToken(string encryptedToken)
        {
            try
            {
                if (string.IsNullOrEmpty(encryptedToken))
                {
                    return new ErrorDataResult<DecryptedQrCodeDto>(null, "QR kod geçersiz.");
                }

                // AES ile şifre çöz
                string decryptedJson = DecryptAES(encryptedToken);

                if (string.IsNullOrEmpty(decryptedJson))
                {
                    return new ErrorDataResult<DecryptedQrCodeDto>(null, "QR kodunun süresi dolmuş. Lütfen yeni bir QR kod alın.");
                }

                // JSON'dan objeye çevir
                var qrData = JsonSerializer.Deserialize<JsonElement>(decryptedJson);

                var result = new DecryptedQrCodeDto
                {
                    MemberId = qrData.GetProperty("MemberId").GetInt32(),
                    ScanNumber = qrData.GetProperty("ScanNumber").GetString(),
                    Timestamp = qrData.GetProperty("Timestamp").GetInt64(),
                    CreatedAt = DateTime.Parse(qrData.GetProperty("CreatedAt").GetString()),
                    IsValid = true
                };

                // Zaman geçerliliğini kontrol et
                if (!IsTokenValid(result.Timestamp))
                {
                    result.IsValid = false;
                    result.ErrorMessage = "QR kodun süresi dolmuş. Lütfen yeni bir QR kod alın.";
                    return new ErrorDataResult<DecryptedQrCodeDto>(result, result.ErrorMessage);
                }

                return new SuccessDataResult<DecryptedQrCodeDto>(result, "QR kod başarıyla çözüldü.");
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<DecryptedQrCodeDto>(null, "QR kod formatı geçersiz.");
            }
        }

        [PerformanceAspect(1)]
        public bool IsTokenValid(long timestamp, int validityMinutes = 5)
        {
            try
            {
                var tokenTime = DateTimeOffset.FromUnixTimeSeconds(timestamp);
                var now = DateTimeOffset.UtcNow;
                var difference = now - tokenTime;

                return difference.TotalMinutes <= validityMinutes;
            }
            catch
            {
                return false;
            }
        }

        private string EncryptAES(string plainText)
        {
            try
            {
                byte[] keyBytes = Encoding.UTF8.GetBytes(EncryptionKey.Substring(0, 32));
                byte[] plainTextBytes = Encoding.UTF8.GetBytes(plainText);

                using (Aes aes = Aes.Create())
                {
                    aes.Key = keyBytes;
                    aes.GenerateIV();

                    using (var encryptor = aes.CreateEncryptor())
                    {
                        byte[] encryptedBytes = encryptor.TransformFinalBlock(plainTextBytes, 0, plainTextBytes.Length);

                        // IV + Encrypted Data
                        byte[] result = new byte[aes.IV.Length + encryptedBytes.Length];
                        Array.Copy(aes.IV, 0, result, 0, aes.IV.Length);
                        Array.Copy(encryptedBytes, 0, result, aes.IV.Length, encryptedBytes.Length);

                        // QR kod okuyucuları için güvenli karakterlerle encode et
                        return EncodeToSafeChars(result);
                    }
                }
            }
            catch
            {
                return string.Empty;
            }
        }

        private string DecryptAES(string encryptedText)
        {
            try
            {
                byte[] keyBytes = Encoding.UTF8.GetBytes(EncryptionKey.Substring(0, 32));
                byte[] encryptedBytes = DecodeFromSafeChars(encryptedText);

                using (Aes aes = Aes.Create())
                {
                    aes.Key = keyBytes;

                    // IV'yi ayır
                    byte[] iv = new byte[aes.IV.Length];
                    byte[] encrypted = new byte[encryptedBytes.Length - iv.Length];

                    Array.Copy(encryptedBytes, 0, iv, 0, iv.Length);
                    Array.Copy(encryptedBytes, iv.Length, encrypted, 0, encrypted.Length);

                    aes.IV = iv;

                    using (var decryptor = aes.CreateDecryptor())
                    {
                        byte[] decryptedBytes = decryptor.TransformFinalBlock(encrypted, 0, encrypted.Length);
                        return Encoding.UTF8.GetString(decryptedBytes);
                    }
                }
            }
            catch
            {
                return string.Empty;
            }
        }

        // QR kod okuyucuları için Base32 encoding (QR uyumlu karakter seti ile)
        private string EncodeToSafeChars(byte[] data)
        {
            if (data == null || data.Length == 0)
                return string.Empty;

            var result = new StringBuilder();
            int buffer = 0;
            int bitsLeft = 0;

            foreach (byte b in data)
            {
                buffer = (buffer << 8) | b;
                bitsLeft += 8;

                while (bitsLeft >= 5)
                {
                    result.Append(SafeChars[(buffer >> (bitsLeft - 5)) & 31]);
                    bitsLeft -= 5;
                }
            }

            if (bitsLeft > 0)
            {
                result.Append(SafeChars[(buffer << (5 - bitsLeft)) & 31]);
            }

            return result.ToString();
        }

        // Base32 decoding (QR uyumlu karakter seti ile)
        private byte[] DecodeFromSafeChars(string encodedText)
        {
            if (string.IsNullOrEmpty(encodedText))
                return new byte[0];

            var result = new List<byte>();
            int buffer = 0;
            int bitsLeft = 0;

            foreach (char c in encodedText)
            {
                int index = SafeChars.IndexOf(c);
                if (index == -1)
                    throw new ArgumentException($"Invalid character '{c}' in encoded text");

                buffer = (buffer << 5) | index;
                bitsLeft += 5;

                if (bitsLeft >= 8)
                {
                    result.Add((byte)((buffer >> (bitsLeft - 8)) & 255));
                    bitsLeft -= 8;
                }
            }

            return result.ToArray();
        }
    }
}
