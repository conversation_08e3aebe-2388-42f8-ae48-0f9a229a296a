﻿using Core.Entities.Concrete;
using Core.Utilities.Results;
using Entities.DTOs;
using System.Threading;
using System.Threading.Tasks;

namespace Business.Abstract
{
    public interface IUserOperationClaimService
    {
        Task<IDataResult<List<UserOperationClaimDto>>> GetUserOperationClaimDetailsAsync(CancellationToken ct = default);
        Task<IResult> AddAsync(UserOperationClaim userOperationClaim, CancellationToken ct = default);
        Task<IResult> DeleteAsync(int id, CancellationToken ct = default);
        Task<IDataResult<List<UserOperationClaim>>> GetAllAsync(CancellationToken ct = default);
        Task<IDataResult<UserOperationClaim>> GetByIdAsync(int id, CancellationToken ct = default);
        Task<IResult> UpdateAsync(UserOperationClaim userOperationClaim, CancellationToken ct = default);
        Task<IResult> InvalidateUserClaimsAsync(int userId, CancellationToken ct = default);
        Task<IResult> AddForRegistrationAsync(UserOperationClaim userOperationClaim, CancellationToken ct = default);
    }
}
