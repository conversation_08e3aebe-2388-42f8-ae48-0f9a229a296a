﻿using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Performance;

using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Business.Concrete
{
    public class CityManager : ICityService
    {
        ICityDal _cityDal;

        public CityManager(ICityDal cityDal)
        {
            _cityDal = cityDal;
        }
       [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(86400)]
        public async Task<IDataResult<List<City>>> GetAllAsync(CancellationToken ct = default)
        {
            var list = await _cityDal.GetAllAsync(ct: ct);
            // Deterministik sonuç (CityName mevcut): sıralama
            list = list.OrderBy(x => x.CityName).ToList();
            return new SuccessDataResult<List<City>>(list);
        }
    }
}
