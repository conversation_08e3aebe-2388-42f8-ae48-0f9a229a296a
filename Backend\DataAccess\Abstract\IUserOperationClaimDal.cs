﻿using Core.DataAccess;
using Core.Entities.Concrete;
using Core.Utilities.Results;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace DataAccess.Abstract
{
    public interface IUserOperationClaimDal : IEntityRepository<UserOperationClaim>
    {
        // Async imzalar
        Task<List<UserOperationClaimDto>> GetUserOperationClaimDetailsAsync(CancellationToken ct = default);
        Task<IResult> InvalidateUserTokensByUserIdAsync(int userId, CancellationToken ct = default);
    }
}
