using Core.CrossCuttingConcerns.Logging;
using Newtonsoft.Json;
using StackExchange.Redis;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Core.CrossCuttingConcerns.Caching
{
    /// <summary>
    /// Redis cache service implementation with error handling and fallback
    /// Multi-tenant aware with JSON serialization support
    /// </summary>
    public class RedisCacheService : ICacheService
    {
        private readonly IDatabase _database;
        private readonly IConnectionMultiplexer _redis;
        private readonly ILogService _logger;
        private readonly TimeSpan _defaultExpiry = TimeSpan.FromMinutes(30);

        public RedisCacheService(IDatabase database, IConnectionMultiplexer redis, ILogService logger)
        {
            _database = database ?? throw new ArgumentNullException(nameof(database));
            _redis = redis ?? throw new ArgumentNullException(nameof(redis));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }


        public async Task<T> GetAsync<T>(string key)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(key))
                    return default(T);

                var value = await _database.StringGetAsync(key);
                
                if (!value.HasValue)
                    return default(T);

                // String type için direkt dönüş
                if (typeof(T) == typeof(string))
                    return (T)(object)value.ToString();

                // Diğer tipler için JSON deserialization - TypeNameHandling.Auto ile
                var settings = new JsonSerializerSettings
                {
                    TypeNameHandling = TypeNameHandling.Auto,
                    NullValueHandling = NullValueHandling.Include
                };
                return JsonConvert.DeserializeObject<T>(value, settings);
            }
            catch (Exception ex)
            {
                _logger.Error($"Redis GetAsync hatası - Key: {key}, Error: {ex.Message}");
                return default(T);
            }
        }


        public async Task SetAsync<T>(string key, T value, TimeSpan? expiry = null)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(key) || value == null)
                    return;

                var expiryTime = expiry ?? _defaultExpiry;
                
                // String type için direkt kayıt
                if (typeof(T) == typeof(string))
                {
                    await _database.StringSetAsync(key, value.ToString(), expiryTime);
                }
                else
                {
                    // Diğer tipler için JSON serialization
                    var jsonValue = JsonConvert.SerializeObject(value, Formatting.None);
                    await _database.StringSetAsync(key, jsonValue, expiryTime);
                }

                _logger.Info($"Redis SetAsync başarılı - Key: {key}, Expiry: {expiryTime.TotalMinutes} dakika");
            }
            catch (Exception ex)
            {
                _logger.Error($"Redis SetAsync hatası - Key: {key}, Error: {ex.Message}");
                // Fallback: Exception fırlatma, sadece log
            }
        }


        public async Task<bool> RemoveAsync(string key)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(key))
                    return false;

                var result = await _database.KeyDeleteAsync(key);
                _logger.Info($"Redis RemoveAsync - Key: {key}, Silindi: {result}");
                return result;
            }
            catch (Exception ex)
            {
                _logger.Error($"Redis RemoveAsync hatası - Key: {key}, Error: {ex.Message}");
                return false;
            }
        }


        public async Task<bool> ExistsAsync(string key)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(key))
                    return false;

                return await _database.KeyExistsAsync(key);
            }
            catch (Exception ex)
            {
                _logger.Error($"Redis ExistsAsync hatası - Key: {key}, Error: {ex.Message}");
                return false;
            }
        }


        public async Task<IEnumerable<string>> GetKeysAsync(string pattern = "*")
        {
            try
            {
                var server = _redis.GetServer(_redis.GetEndPoints().First());
                var keys = server.KeysAsync(pattern: pattern);
                var result = new List<string>();
                
                await foreach (var key in keys)
                {
                    result.Add(key.ToString());
                }
                
                return result;
            }
            catch (Exception ex)
            {
                _logger.Error($"Redis GetKeysAsync hatası - Pattern: {pattern}, Error: {ex.Message}");
                return new List<string>();
            }
        }



        public async Task<long> RemoveByPatternAsync(string pattern)
        {
            try
            {
                var keys = (await GetKeysAsync(pattern)).ToArray();
                if (keys.Length == 0)
                    return 0;

                var redisKeys = keys.Select(k => (RedisKey)k).ToArray();
                var deletedCount = await _database.KeyDeleteAsync(redisKeys);
                
                _logger.Info($"Redis RemoveByPatternAsync - Pattern: {pattern}, Silinen: {deletedCount}");
                return deletedCount;
            }
            catch (Exception ex)
            {
                _logger.Error($"Redis RemoveByPatternAsync hatası - Pattern: {pattern}, Error: {ex.Message}");
                return 0;
            }
        }

    }
}
