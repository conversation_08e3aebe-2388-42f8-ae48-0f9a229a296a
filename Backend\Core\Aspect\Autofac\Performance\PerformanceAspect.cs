﻿using Castle.DynamicProxy;
using Core.CrossCuttingConcerns.Logging;
using Core.CrossCuttingConcerns.Logging.FileLogger;
using Core.Utilities.Interceptors;
using Core.Utilities.IoC;
using Microsoft.Extensions.DependencyInjection;
using System.Collections.Concurrent;
using System.Diagnostics;
using System.Threading.Tasks;


namespace Core.Aspects.Autofac.Performance
{
    /// <summary>
    /// Async metodlar için performans ölçüm aspect'i
    /// Tek dosyada async davranış; thread-safe olması için lokal Stopwatch kullanır.
    /// </summary>
    public class PerformanceAspect : MethodInterceptionAsync
    {
        private readonly int _interval;
        private readonly ILogService _performanceLoggerService;

        public PerformanceAspect(int interval)
        {
            _interval = interval;
            _performanceLoggerService = ServiceTool.ServiceProvider.GetService<PerformanceLoggerService>();
            Priority = 4; // Log'dan sonra - Performans ölçümü
        }

        private readonly ConcurrentDictionary<IInvocation, Stopwatch> _stopwatches = new ConcurrentDictionary<IInvocation, Stopwatch>();

        protected override void OnBefore(IInvocation invocation)
        {
            var stopwatch = new Stopwatch();
            stopwatch.Start();
            _stopwatches[invocation] = stopwatch;
        }

        protected override void OnAfter(IInvocation invocation)
        {
            if (_stopwatches.TryRemove(invocation, out var stopwatch))
            {
                stopwatch.Stop();
                LogIfExceeded(invocation, stopwatch);
            }
        }

        protected override void OnException(IInvocation invocation, Exception e)
        {
            if (_stopwatches.TryRemove(invocation, out var stopwatch))
            {
                stopwatch.Stop();
                LogIfExceeded(invocation, stopwatch);
            }
        }



        private static void LogIfExceeded(IInvocation invocation, Stopwatch stopwatch)
        {
            var attr = GetAttribute(invocation) as PerformanceAspect;
            if (attr == null) return;
            if (stopwatch.Elapsed.TotalSeconds > attr._interval)
            {
                var logger = attr._performanceLoggerService;
                var logMessage = $"Performance: {invocation.Method.DeclaringType.FullName}.{invocation.Method.Name} took {stopwatch.Elapsed.TotalSeconds} seconds";
                logger?.LogPerformance(logMessage);
            }
        }

        private static MethodInterceptionBaseAttribute GetAttribute(IInvocation invocation)
        {
            var attrs = invocation.MethodInvocationTarget
                .GetCustomAttributes(typeof(PerformanceAspect), true);
            if (attrs != null && attrs.Length > 0)
            {
                return (MethodInterceptionBaseAttribute)attrs[0];
            }
            return null;
        }
    }
}