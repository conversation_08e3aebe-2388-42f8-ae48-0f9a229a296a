﻿using Core.Utilities.Results;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Business.Abstract
{
    public  interface ICompanyService
    {
        Task<IDataResult<List<Company>>> GetAllAsync(CancellationToken ct = default);
        Task<IDataResult<Company>> GetByIdAsync(int id, CancellationToken ct = default);
        Task<IResult> AddAsync(Company company, CancellationToken ct = default);
        Task<IResult> UpdateAsync(Company company, CancellationToken ct = default);
        Task<IResult> DeleteAsync(int id, CancellationToken ct = default);
        Task<IDataResult<List<ActiveCompanyDetailDto>>> GetActiveCompaniesAsync(CancellationToken ct = default);

    }
}
