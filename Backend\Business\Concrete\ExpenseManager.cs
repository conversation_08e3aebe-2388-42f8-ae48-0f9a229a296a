using Business.Abstract;
using Business.Constants;
using Business.ValidationRules.FluentValidation;
using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Validation;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Transaction;

using Core.Utilities.Results;
using Core.Utilities.Paging;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Business.BusinessAscpects.Autofac;
using Core.Utilities.Security.CompanyContext;

namespace Business.Concrete
{
    public class ExpenseManager : IExpenseService
    {
        private readonly IExpenseDal _expenseDal;
        private readonly ICompanyContext _companyContext;

        public ExpenseManager(IExpenseDal expenseDal, ICompanyContext companyContext)
        {
            _expenseDal = expenseDal;
            _companyContext = companyContext;
        }

        [SecuredOperation("owner,admin")]
        [ValidationAspect(typeof(ExpenseValidator))]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("Expense")]
        public async Task<IResult> AddAsync(Expense expense, CancellationToken ct = default)
        {
            var companyId = _companyContext.GetCompanyId();
            return await _expenseDal.AddExpenseWithBusinessLogicAsync(expense, companyId, ct);
        }

        [SecuredOperation("owner,admin")]
        [ValidationAspect(typeof(ExpenseValidator))] // Validator eklendi
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("Expense")]
        public async Task<IResult> UpdateAsync(Expense expense, CancellationToken ct = default)
        {
            var companyId = _companyContext.GetCompanyId();
            return await _expenseDal.UpdateExpenseWithBusinessLogicAsync(expense, companyId, ct);
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("Expense")]
        public async Task<IResult> DeleteAsync(int expenseId, CancellationToken ct = default)
        {
            var companyId = _companyContext.GetCompanyId();
            return await _expenseDal.SoftDeleteExpenseAsync(expenseId, companyId, ct);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(1800)]
        public async Task<IDataResult<List<Expense>>> GetAllAsync(CancellationToken ct = default)
        {
            var companyId = _companyContext.GetCompanyId();
            var list = await _expenseDal.GetAllAsync(e => e.CompanyID == companyId && e.IsActive == true, ct);
            return new SuccessDataResult<List<Expense>>(list);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(1800)]
        public async Task<IDataResult<Expense>> GetByIdAsync(int expenseId, CancellationToken ct = default)
        {
            var companyId = _companyContext.GetCompanyId();
            var entity = await _expenseDal.GetAsync(e => e.ExpenseID == expenseId && e.CompanyID == companyId && e.IsActive == true, ct);
            if (entity == null)
            {
                return new ErrorDataResult<Expense>(Business.Constants.Messages.ExpenseNotFound);
            }
            return new SuccessDataResult<Expense>(entity);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(1800)]
        public async Task<IDataResult<List<ExpenseDto>>> GetExpensesByDateRangeAsync(DateTime startDate, DateTime endDate, CancellationToken ct = default)
        {
            var data = await _expenseDal.GetExpensesByDateRangeAsync(startDate, endDate, ct);
            return new SuccessDataResult<List<ExpenseDto>>(data);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(600)]
        public async Task<IDataResult<ExpenseDashboardDto>> GetExpenseDashboardDataAsync(int year, int month, CancellationToken ct = default)
        {
            var data = await _expenseDal.GetExpenseDashboardDataAsync(year, month, ct);
            return new SuccessDataResult<ExpenseDashboardDto>(data);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(1800)]
        public async Task<IDataResult<MonthlyExpenseDto>> GetMonthlyExpenseAsync(int year, CancellationToken ct = default)
        {
            var data = await _expenseDal.GetMonthlyExpenseAsync(year, ct);
            return new SuccessDataResult<MonthlyExpenseDto>(data);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(600)]
        public async Task<IDataResult<ExpenseTotals>> GetExpenseTotalsAsync(ExpensePagingParameters parameters, CancellationToken ct = default)
        {
            var data = await _expenseDal.GetExpenseTotalsAsync(parameters, ct);
            return new SuccessDataResult<ExpenseTotals>(data);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(300)]
        public async Task<IDataResult<PaginatedResult<ExpenseDto>>> GetExpensesPaginatedAsync(ExpensePagingParameters parameters, CancellationToken ct = default)
        {
            var data = await _expenseDal.GetExpensesPaginatedAsync(parameters, ct);
            return new SuccessDataResult<PaginatedResult<ExpenseDto>>(data);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(300)]
        public async Task<IDataResult<List<ExpenseDto>>> GetAllExpensesFilteredAsync(ExpensePagingParameters parameters, CancellationToken ct = default)
        {
            var data = await _expenseDal.GetAllExpensesFilteredAsync(parameters, ct);
            return new SuccessDataResult<List<ExpenseDto>>(data);
        }










    }
}