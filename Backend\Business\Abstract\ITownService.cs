﻿using Core.Entities.Concrete;
using Core.Utilities.Results;
using Entities.Concrete;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Business.Abstract
{
    public interface ITownService
    {
        // Async yüzey
        Task<IDataResult<List<Town>>> GetAllAsync(CancellationToken ct = default);
        Task<IDataResult<List<Town>>> GetByCityIdAsync(int cityId, CancellationToken ct = default);

    }
}
