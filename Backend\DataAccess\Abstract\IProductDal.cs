﻿using Core.DataAccess;
using Core.Utilities.Paging;
using Core.Utilities.Results;
using Entities.Concrete;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace DataAccess.Abstract
{
    public interface IProductDal : IEntityRepository<Product>
    {
        // ASYNC imzalar (CT'li)
        Task<PaginatedResult<Product>> GetAllPaginatedAsync(ProductPagingParameters parameters, CancellationToken ct = default);
        Task<IResult> SoftDeleteProductAsync(int productId, int companyId, CancellationToken ct = default);
        Task<IResult> UpdateProductWithBusinessLogicAsync(Product product, int companyId, CancellationToken ct = default);
        Task<IDataResult<Product>> GetProductByIdWithValidationAsync(int productId, int companyId, CancellationToken ct = default);

    }
}
