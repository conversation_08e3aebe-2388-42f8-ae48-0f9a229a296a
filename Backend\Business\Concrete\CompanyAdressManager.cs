﻿using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Business.ValidationRules.FluentValidation;
using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Validation;
using Core.Utilities.Results;
using DataAccess.Abstract;
using DataAccess.Concrete.EntityFramework;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Business.Concrete
{
    public class CompanyAdressManager : ICompanyAdressService
    {
        ICompanyAdressDal _companyAdressDal;

        public CompanyAdressManager(ICompanyAdressDal companyAdress)
        {
            _companyAdressDal = companyAdress;
        }
        [SecuredOperation("owner")]
        [ValidationAspect(typeof(CompanyAdressValidator))]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("CompanyAdress")]
        public async Task<IResult> AddAsync(CompanyAdress companyAdress, CancellationToken ct = default)
        {
            await _companyAdressDal.AddAsync(companyAdress, ct);
            return new SuccessResult(Messages.CompanyAdressAdded);
        }
        [SecuredOperation("owner")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("CompanyAdress")]
        public async Task<IResult> DeleteAsync(int id, CancellationToken ct = default)
        {
            await _companyAdressDal.DeleteAsync(id, ct);
            return new SuccessResult(Messages.CompanyAdressDeleted);
        }
        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        [CacheAspect(1800)] // 30 dakika cache - Admin paneli için
        public async Task<IDataResult<List<CompanyAdress>>> GetAllAsync(CancellationToken ct = default)
        {
            var list = await _companyAdressDal.GetAllAsync(ct: ct);
            // deterministik sıralama için bir alan yoksa ID üzerinden sırala
            list = list.OrderBy(x => x.CompanyAdressID).ToList();
            return new SuccessDataResult<List<CompanyAdress>>(list);
        }
        [SecuredOperation("owner")]
        [ValidationAspect(typeof(CompanyAdressValidator))]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("CompanyAdress")]
        public async Task<IResult> UpdateAsync(CompanyAdress companyAdress, CancellationToken ct = default)
        {
            await _companyAdressDal.UpdateAsync(companyAdress, ct);
            return new SuccessResult(Messages.CompanyAdressUpdated);
        }
        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        [CacheAspect(1800)] // 30 dakika cache - Admin paneli için
        public async Task<IDataResult<List<CompanyAdressDetailDto>>> GetCompanyAdressDetailsAsync(CancellationToken ct = default)
        {
            var list = await _companyAdressDal.GetCompanyAdressDetailsAsync(ct);
            // deterministik sıralama için ID üzerinden sırala
            list = list.OrderBy(x => x.CompanyAdressID).ToList();
            return new SuccessDataResult<List<CompanyAdressDetailDto>>(list);
        }

    }
}
