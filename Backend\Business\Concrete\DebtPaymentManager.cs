﻿using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Transaction;

using Core.Utilities.Results;
using DataAccess.Abstract;
using System.Threading;
using System.Threading.Tasks;

namespace Business.Concrete
{
    public class DebtPaymentManager : IDebtPaymentService
    {
        private readonly IDebtPaymentDal _debtPaymentDal;

        public DebtPaymentManager(IDebtPaymentDal debtPaymentDal)
        {
            _debtPaymentDal = debtPaymentDal;
        }



        // ASYNC versiyon
        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("DebtPayment")]
        public async Task<IResult> DeleteAsync(int debtPaymentId, CancellationToken ct = default)
        {
            return await _debtPaymentDal.DeleteDebtPaymentWithRemainingDebtUpdateAsync(debtPaymentId, ct);
        }
    }
}
