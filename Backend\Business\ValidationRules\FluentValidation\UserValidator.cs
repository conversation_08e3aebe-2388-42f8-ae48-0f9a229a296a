﻿using Core.Entities.Concrete;
using DataAccess.Concrete.EntityFramework;
using Entities.Concrete;
using FluentValidation;
using FluentValidation.Validators;
using Microsoft.IdentityModel.Tokens;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Numerics;
using System.Text;
using System.Threading.Tasks;

namespace Business.ValidationRules.FluentValidation
{
    public class UserValidator : AbstractValidator<User>
    {
        public UserValidator()
        {
            RuleFor(x => x.FirstName).NotEmpty().WithMessage("Ad kısmı boş bırakılamaz.");
            RuleFor(x => x.FirstName).MinimumLength(2).WithMessage("İsim kısmı en az 2 karakter olmalıdır.");
            RuleFor(x => x.LastName).NotEmpty().WithMessage("Soyad kısmı boş bırakılamaz.");
            RuleFor(x => x.PasswordHash).NotEmpty().WithMessage("Şifre kısmı boş bırakılamaz.");
            RuleFor(x => x.PasswordSalt).NotEmpty().WithMessage("Şifre kısmı boş bırakılamaz.");
            RuleFor(x => x.Email).EmailAddress().WithMessage("E Posta adresi boş bırakılamaz.");
        }
    }
}
