using Entities.DTOs;
using FluentValidation;

namespace Business.ValidationRules.FluentValidation
{
    public class CompanyExerciseAddValidator : AbstractValidator<CompanyExerciseAddDto>
    {
        public CompanyExerciseAddValidator()
        {
            RuleFor(x => x.ExerciseCategoryID)
                .GreaterThan(0).WithMessage("Kategori seçimi yapılmalıdır.");

            RuleFor(x => x.ExerciseName)
                .NotEmpty().WithMessage("Egzersiz adı boş bırakılamaz.")
                .MinimumLength(2).WithMessage("Egzersiz adı en az 2 karakter olmalıdır.")
                .MaximumLength(200).WithMessage("Egzersiz adı en fazla 200 karakter olabilir.");

            RuleFor(x => x.Description)
                .MaximumLength(1000).WithMessage("Açıklama en fazla 1000 karakter olabilir.");

            RuleFor(x => x.Instructions)
                .MaximumLength(2000).WithMessage("Talimatlar en fazla 2000 karakter olabilir.");

            RuleFor(x => x.MuscleGroups)
                .MaximumLength(500).WithMessage("Kas grupları alanı en fazla 500 karakter olabilir.");

            RuleFor(x => x.Equipment)
                .MaximumLength(200).WithMessage("Ekipman alanı en fazla 200 karakter olabilir.");

            RuleFor(x => x.DifficultyLevel)
                .InclusiveBetween((byte)1, (byte)5)
                .When(x => x.DifficultyLevel.HasValue)
                .WithMessage("Zorluk seviyesi 1 ile 5 arasında olmalıdır.");
        }
    }

    public class CompanyExerciseUpdateValidator : AbstractValidator<CompanyExerciseUpdateDto>
    {
        public CompanyExerciseUpdateValidator()
        {
            RuleFor(x => x.CompanyExerciseID)
                .GreaterThan(0).WithMessage("Geçerli bir egzersiz ID'si gereklidir.");

            RuleFor(x => x.ExerciseCategoryID)
                .GreaterThan(0).WithMessage("Kategori seçimi yapılmalıdır.");

            RuleFor(x => x.ExerciseName)
                .NotEmpty().WithMessage("Egzersiz adı boş bırakılamaz.")
                .MinimumLength(2).WithMessage("Egzersiz adı en az 2 karakter olmalıdır.")
                .MaximumLength(200).WithMessage("Egzersiz adı en fazla 200 karakter olabilir.");

            RuleFor(x => x.Description)
                .MaximumLength(1000).WithMessage("Açıklama en fazla 1000 karakter olabilir.");

            RuleFor(x => x.Instructions)
                .MaximumLength(2000).WithMessage("Talimatlar en fazla 2000 karakter olabilir.");

            RuleFor(x => x.MuscleGroups)
                .MaximumLength(500).WithMessage("Kas grupları alanı en fazla 500 karakter olabilir.");

            RuleFor(x => x.Equipment)
                .MaximumLength(200).WithMessage("Ekipman alanı en fazla 200 karakter olabilir.");

            RuleFor(x => x.DifficultyLevel)
                .InclusiveBetween((byte)1, (byte)5)
                .When(x => x.DifficultyLevel.HasValue)
                .WithMessage("Zorluk seviyesi 1 ile 5 arasında olmalıdır.");
        }
    }
}

