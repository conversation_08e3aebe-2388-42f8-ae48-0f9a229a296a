﻿using Core.DataAccess;
using Core.Entities.Concrete;
using Core.Utilities.Results;
using Entities.Concrete;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace DataAccess.Abstract
{
    public interface IOperationClaimDal : IEntityRepository<OperationClaim>
    {
        // SOLID prensiplerine uygun: Business logic DAL katmanında
        Task<IDataResult<OperationClaim>> GetOperationClaimByNameWithValidationAsync(string name, CancellationToken ct = default);
    }
}
