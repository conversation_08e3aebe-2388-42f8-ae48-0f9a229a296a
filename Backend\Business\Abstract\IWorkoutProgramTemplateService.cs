using Core.Utilities.Results;
using Entities.Concrete;
using Entities.DTOs;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace Business.Abstract
{
    public interface IWorkoutProgramTemplateService
    {
        // Async yüzeyler
        Task<IDataResult<List<WorkoutProgramTemplateListDto>>> GetAllAsync(CancellationToken ct = default);
        Task<IDataResult<WorkoutProgramTemplateDto>> GetByIdAsync(int templateId, CancellationToken ct = default);
        Task<IResult> AddAsync(WorkoutProgramTemplateAddDto templateAddDto, CancellationToken ct = default);
        Task<IResult> UpdateAsync(WorkoutProgramTemplateUpdateDto templateUpdateDto, CancellationToken ct = default);
        Task<IResult> DeleteAsync(int templateId, CancellationToken ct = default);
    }
}
