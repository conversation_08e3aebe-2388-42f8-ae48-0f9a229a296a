using Core.Utilities.Results;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace DataAccess.Abstract
{
    public interface IUnifiedCompanyDal
    {
        Task<IDataResult<string>> AddUnifiedCompanyWithAllEntitiesAsync(UnifiedCompanyAddDto unifiedCompanyDto, CancellationToken ct = default);
    }
}
