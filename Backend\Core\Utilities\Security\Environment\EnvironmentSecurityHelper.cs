using System;

namespace Core.Utilities.Security.Environment
{
    /// <summary>
    /// Environment bazlı güvenlik ayarları için yardımcı sınıf
    /// Production ortamında environment variables kullanılması önerilir
    /// </summary>
    public static class EnvironmentSecurityHelper
    {
        /// <summary>
        /// Environment'a göre SecurityKey'i alır
        /// Production'da environment variable'dan okur, yoksa varsayılan değeri kullanır
        /// </summary>
        /// <param name="environment">Environment adı (dev, staging, canlı)</param>
        /// <param name="fallbackKey">Environment variable bulunamazsa kullanılacak varsayılan key</param>
        /// <returns>SecurityKey</returns>
        public static string GetSecurityKey(string environment, string fallbackKey)
        {
            // Production ortamında environment variable'dan oku
            var envVarName = $"GYM_SECURITY_KEY_{environment.ToUpper()}";
            var envKey = System.Environment.GetEnvironmentVariable(envVarName);

            if (!string.IsNullOrEmpty(envKey))
            {
                // Production'da environment variable kullanıldığını logla (güvenlik için key'i loglama!)
                Console.WriteLine($"[SECURITY] Environment variable '{envVarName}' kullanılıyor - Environment: {environment}");
                return envKey;
            }

            // Environment variable yoksa fallback key'i kullan
            if (environment.ToLower() == "canlı" || environment.ToLower() == "production")
            {
                Console.WriteLine($"[SECURITY WARNING] Production ortamında environment variable '{envVarName}' bulunamadı! Fallback key kullanılıyor.");
            }

            return fallbackKey;
        }
        
        /// <summary>
        /// SecurityKey'in minimum güvenlik gereksinimlerini kontrol eder
        /// </summary>
        /// <param name="securityKey">Kontrol edilecek key</param>
        /// <returns>Key güvenli ise true</returns>
        public static bool ValidateSecurityKey(string securityKey)
        {
            if (string.IsNullOrEmpty(securityKey))
                return false;
                
            // Minimum 64 karakter olmalı
            if (securityKey.Length < 64)
                return false;
                
            // Sadece alfanumerik ve özel karakterler içermeli
            return System.Text.RegularExpressions.Regex.IsMatch(securityKey, @"^[a-zA-Z0-9_\-]+$");
        }
        
        /// <summary>
        /// Environment'a göre güvenlik seviyesini belirler
        /// </summary>
        /// <param name="environment">Environment adı</param>
        /// <returns>Güvenlik seviyesi</returns>
        public static SecurityLevel GetSecurityLevel(string environment)
        {
            return environment?.ToLower() switch
            {
                "dev" => SecurityLevel.Development,
                "staging" => SecurityLevel.Staging,
                "canlı" or "production" => SecurityLevel.Production,
                _ => SecurityLevel.Development
            };
        }
    }
    
    /// <summary>
    /// Güvenlik seviyeleri
    /// </summary>
    public enum SecurityLevel
    {
        Development = 1,
        Staging = 2,
        Production = 3
    }
}
