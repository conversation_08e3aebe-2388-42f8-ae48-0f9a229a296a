using Core.DataAccess.EntityFramework;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfUserCompanyDal: EfEntityRepositoryBase<UserCompany, GymContext>, IUserCompanyDal
    {
        // Constructor injection (Scalability için)
        public EfUserCompanyDal(GymContext context) : base(context)
        {
        }

        // ASYNC implementasyonlar
        public async Task<List<UserCompanyDetailDto>> GetUserCompanyDetailsAsync(CancellationToken ct = default)
        {
            IQueryable<UserCompanyDetailDto> query = from uc in _context.UserCompanies.AsNoTracking()
                                                     join cu in _context.CompanyUsers.AsNoTracking() on uc.UserID equals cu.CompanyUserID
                                                     join c in _context.Companies.AsNoTracking() on uc.CompanyId equals c.CompanyID
                                                     where uc.IsActive == true
                                                     orderby uc.UserCompanyID
                                                     select new UserCompanyDetailDto
                                                     {
                                                         UserCompanyId = uc.UserCompanyID,
                                                         CompanyUserName = cu.Name,
                                                         CompanyName = c.CompanyName,
                                                         isActive = uc.IsActive,
                                                     };
            return await query.ToListAsync(ct);
        }

        public async Task<int> GetUserCompanyIdAsync(int userId, CancellationToken ct = default)
        {
            // N+1 optimizasyonu: Tek join sorgusu ile 3 tabloyu birleştir
            var result = await (from u in _context.Users.AsNoTracking()
                               join cu in _context.CompanyUsers.AsNoTracking() on u.Email equals cu.Email
                               join uc in _context.UserCompanies.AsNoTracking() on cu.CompanyUserID equals uc.UserID
                               where u.UserID == userId && uc.IsActive == true
                               select uc.CompanyId)
                               .FirstOrDefaultAsync(ct);

            return result == 0 ? -1 : result;
        }

        public async Task<List<UserCompany>> GetActiveUserCompaniesAsync(int userId, CancellationToken ct = default)
        {
            return await _context.UserCompanies.AsNoTracking()
                .Where(uc => uc.UserID == userId && uc.IsActive == true)
                .OrderBy(uc => uc.UserCompanyID)
                .ToListAsync(ct);
        }

        public async Task<IResult> AddUserCompanyWithValidationAsync(UserCompany userCompany, CancellationToken ct = default)
        {
            try
            {
                var user = await _context.Users.AsNoTracking().FirstOrDefaultAsync(u => u.UserID == userCompany.UserID, ct);
                if (user == null)
                {
                    return new ErrorResult("Geçersiz kullanıcı ID'si. Lütfen geçerli bir kullanıcı seçiniz.");
                }

                userCompany.CreationDate = DateTime.Now;
                userCompany.IsActive = true;

                await _context.UserCompanies.AddAsync(userCompany, ct);
                await _context.SaveChangesAsync(ct);

                return new SuccessResult("Kullanıcı şirket ilişkisi başarıyla eklendi.");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Kullanıcı şirket ilişkisi eklenirken hata oluştu: {ex.Message}");
            }
        }

        public async Task<IResult> UpdateActiveCompanyWithValidationAsync(int userId, int companyId, CancellationToken ct = default)
        {
            try
            {
                var userCompanies = await _context.UserCompanies
                    .Where(uc => uc.UserID == userId)
                    .ToListAsync(ct);

                var targetCompany = userCompanies.FirstOrDefault(uc => uc.CompanyId == companyId);
                if (targetCompany == null)
                {
                    return new ErrorResult("Kullanıcı şirket ilişkisi bulunamadı.");
                }

                if (targetCompany.IsActive != true)
                {
                    return new ErrorResult("Şirket aktif değil.");
                }

                targetCompany.UpdatedDate = DateTime.Now;
                await _context.SaveChangesAsync(ct);

                return new SuccessResult("Aktif şirket başarıyla güncellendi.");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Aktif şirket güncellenirken hata oluştu: {ex.Message}");
            }
        }

    }
}
