﻿using Core.Utilities.Results;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Utilities.Business
{
    public class BusinessRules
    {
        public static async Task<IResult?> RunAsync(params Task<IResult?>[] logics)
        {
            foreach (var logicTask in logics)
            {
                var result = await logicTask.ConfigureAwait(false);
                if (result is { Success: false })
                    return result;
            }
            return null;
        }

        public static async Task<IResult?> RunAsync(IEnumerable<Func<Task<IResult?>>> logics)
        {
            foreach (var logic in logics)
            {
                var result = await logic().ConfigureAwait(false);
                if (result is { Success: false })
                    return result;
            }
            return null;
        }

    }
}
