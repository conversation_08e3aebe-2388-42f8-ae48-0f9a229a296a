﻿using Core.Utilities.Results;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Business.Abstract
{
    public interface IRemainingDebtService
    {

        // Async yüzeyler
        Task<IDataResult<List<RemainingDebtDetailDto>>> GetRemainingDebtDetailsAsync(CancellationToken ct = default);
        Task<IResult> AddDebtPaymentAsync(DebtPaymentDto debtPaymentDto, CancellationToken ct = default);
        Task<IResult> DeleteAsync(int remainingDebtId, CancellationToken ct = default);
    }
}
