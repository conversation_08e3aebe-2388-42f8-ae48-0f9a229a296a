﻿using Core.DataAccess;
using Core.Entities.Concrete;
using Core.Utilities.Results;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace DataAccess.Abstract
{
    public interface IUserDeviceDal: IEntityRepository<UserDevice>
    {

        // ASYNC imzalar (CT ile) — sync'i silme
        Task<List<UserDevice>> GetActiveDevicesByUserIdAsync(int userId, CancellationToken ct = default);
        Task<UserDevice> GetByRefreshTokenAsync(string refreshToken, CancellationToken ct = default);
        Task<IResult> AddDeviceWithManagementAsync(UserDevice device, CancellationToken ct = default);
        Task CleanExpiredTokensAsync(CancellationToken ct = default);
        Task<IResult> RevokeAllDevicesExceptCurrentAsync(int userId, string currentRefreshToken, CancellationToken ct = default);
        Task<IDataResult<UserDevice>> GetByRefreshTokenWithValidationAsync(string refreshToken, CancellationToken ct = default);
        Task<IResult> RevokeDeviceWithValidationAsync(int deviceId, CancellationToken ct = default);
    }
}
