﻿using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;

using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Transaction;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Business.Concrete
{
    public class UserLicenseManager : IUserLicenseService
    {
        private readonly IUserLicenseDal _userLicenseDal;

        public UserLicenseManager(IUserLicenseDal userLicenseDal)
        {
            _userLicenseDal = userLicenseDal;
        }

        // ASYNC versiyonlar (sync korunur)
        [SecuredOperation("owner")]
        [LogAspect]
        [PerformanceAspect(3)]
        public async Task<IResult> AddAsync(UserLicense userLicense, CancellationToken ct = default)
        {
            return await _userLicenseDal.AddUserLicenseWithDateManagementAsync(userLicense, ct);
        }

        [SecuredOperation("owner")]
        [LogAspect]
        public async Task<IResult> DeleteAsync(int id, CancellationToken ct = default)
        {
            await _userLicenseDal.DeleteAsync(id, ct);
            return new SuccessResult("Kullanıcı lisansı başarıyla silindi");
        }

        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        public async Task<IDataResult<List<UserLicenseDto>>> GetAllAsync(CancellationToken ct = default)
        {
            var data = await _userLicenseDal.GetUserLicenseDetailsAsync(ct);
            return new SuccessDataResult<List<UserLicenseDto>>(data);
        }

        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        public async Task<IDataResult<PaginatedUserLicenseDto>> GetAllPaginatedAsync(int page, int pageSize, string searchTerm, string sortBy, string companyName, int? remainingDaysMin, int? remainingDaysMax, CancellationToken ct = default)
        {
            var result = await _userLicenseDal.GetUserLicenseDetailsPaginatedAsync(page, pageSize, searchTerm, sortBy, companyName, remainingDaysMin, remainingDaysMax, ct);
            return new SuccessDataResult<PaginatedUserLicenseDto>(result);
        }

        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        public async Task<IDataResult<PaginatedUserLicenseDto>> GetExpiredAndPassiveAsync(int page, int pageSize, string searchTerm, CancellationToken ct = default)
        {
            var result = await _userLicenseDal.GetExpiredAndPassiveLicensesAsync(page, pageSize, searchTerm, ct);
            return new SuccessDataResult<PaginatedUserLicenseDto>(result);
        }

        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        public async Task<IDataResult<List<UserLicenseDto>>> GetActiveByUserIdAsync(int userId, CancellationToken ct = default)
        {
            var data = await _userLicenseDal.GetActiveUserLicensesByUserIdAsync(userId, ct);
            return new SuccessDataResult<List<UserLicenseDto>>(data);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public async Task<IDataResult<List<UserLicenseDto>>> GetMyActiveLicensesAsync(int userId, CancellationToken ct = default)
        {
            var data = await _userLicenseDal.GetActiveUserLicensesByUserIdAsync(userId, ct);
            return new SuccessDataResult<List<UserLicenseDto>>(data);
        }

        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        public async Task<IDataResult<UserLicenseDto>> GetByIdAsync(int id, CancellationToken ct = default)
        {
            var data = await _userLicenseDal.GetUserLicenseDetailAsync(id, ct);
            return new SuccessDataResult<UserLicenseDto>(data);
        }

        [SecuredOperation("owner")]
        [LogAspect]
        [PerformanceAspect(3)]
        public async Task<IResult> UpdateAsync(UserLicense userLicense, CancellationToken ct = default)
        {
            return await _userLicenseDal.UpdateUserLicenseWithDateManagementAsync(userLicense, ct);
        }

        [SecuredOperation("owner")]
        [LogAspect]
        [PerformanceAspect(3)]
        public async Task<IResult> PurchaseAsync(LicensePurchaseDto licensePurchaseDto, CancellationToken ct = default)
        {
            return await _userLicenseDal.PurchaseLicenseAsync(licensePurchaseDto, ct);
        }

        [SecuredOperation("owner")]
        [LogAspect]
        [PerformanceAspect(3)]
        public async Task<IResult> ExtendLicenseByPackageAsync(LicenseExtensionByPackageDto licenseExtensionByPackageDto, CancellationToken ct = default)
        {
            return await _userLicenseDal.ExtendLicenseByPackageAsync(licenseExtensionByPackageDto, ct);
        }

        [SecuredOperation("owner")]
        [LogAspect]
        [PerformanceAspect(3)]
        public async Task<IResult> RevokeLicenseAsync(int userLicenseId, CancellationToken ct = default)
        {
            return await _userLicenseDal.RevokeLicenseWithValidationAsync(userLicenseId, ct);
        }

        public async Task<IDataResult<List<string>>> GetUserRolesAsync(int userId, CancellationToken ct = default)
        {
            var roles = await _userLicenseDal.GetUserRolesAsync(userId, ct);
            return new SuccessDataResult<List<string>>(roles);
        }

    }
}