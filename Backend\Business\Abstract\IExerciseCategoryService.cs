using Core.Utilities.Results;
using Entities.Concrete;
using Entities.DTOs;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace Business.Abstract
{
    public interface IExerciseCategoryService
    {
        // Async imzalar
        Task<IDataResult<List<ExerciseCategoryDto>>> GetAllCategoriesAsync(CancellationToken ct = default);
        Task<IDataResult<List<ExerciseCategoryDto>>> GetActiveCategoriesAsync(CancellationToken ct = default);
        Task<IDataResult<ExerciseCategoryDto>> GetByIdAsync(int categoryId, CancellationToken ct = default);
        Task<IResult> AddAsync(ExerciseCategoryAddDto categoryAddDto, CancellationToken ct = default);
        Task<IResult> UpdateAsync(ExerciseCategoryUpdateDto categoryUpdateDto, CancellationToken ct = default);
        Task<IResult> DeleteAsync(int categoryId, CancellationToken ct = default);
    }
}
