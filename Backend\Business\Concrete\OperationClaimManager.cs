﻿using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Business.ValidationRules.FluentValidation;
using Core.Aspects.Autofac.Validation;


using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Entities.Concrete;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Business.Concrete
{
    public class OperationClaimManager : IOperationClaimService
    {
        IOperationClaimDal _operationClaimDal;

        public OperationClaimManager(IOperationClaimDal operationClaimDal)
        {
            _operationClaimDal = operationClaimDal;
        }
        // Async varyantlar
        [SecuredOperation("owner")]
        [ValidationAspect(typeof(OperationClaimValidator))]
        [SmartCacheRemoveAspectAsync("OperationClaim")]
        public async Task<IResult> AddAsync(OperationClaim operationClaim, CancellationToken ct = default)
        {
            await _operationClaimDal.AddAsync(operationClaim, ct);
            return new SuccessResult(Messages.OperationClaimAdded);
        }

        [SecuredOperation("owner")]
        [SmartCacheRemoveAspectAsync("OperationClaim")]
        public async Task<IResult> DeleteAsync(int id, CancellationToken ct = default)
        {
            try
            {
                await _operationClaimDal.HardDeleteAsync(id, ct);
                return new SuccessResult(Messages.OperationClaimDeleted);
            }
            catch (DbUpdateException)
            {
                // Büyük olasılıkla FK ihlali (kullanıcı-rol ilişkisi mevcut)
                return new ErrorResult("Rol başka kayıtlar tarafından kullanılıyor. Önce ilişkili kullanıcı rolleri kaldırılmalı.");
            }
            catch (Exception)
            {
                return new ErrorResult("Rol silinirken beklenmeyen bir hata oluştu.");
            }
        }

        [SecuredOperation("owner")]
        [CacheAspect(86400)] // 24 saat cache - System roles (master data)
        public async Task<IDataResult<List<OperationClaim>>> GetAllAsync(CancellationToken ct = default)
        {
            var list = await _operationClaimDal.GetAllAsync(ct: ct);
            list = list.OrderBy(x => x.OperationClaimId).ToList();
            return new SuccessDataResult<List<OperationClaim>>(list, Messages.OperationClaimsListed);
        }

        [SecuredOperation("owner")]
        [CacheAspect(86400)] // 24 saat cache - Rol detayı
        public async Task<IDataResult<OperationClaim>> GetByIdAsync(int id, CancellationToken ct = default)
        {
            var entity = await _operationClaimDal.GetAsync(o => o.OperationClaimId == id, ct);
            return new SuccessDataResult<OperationClaim>(entity);
        }

        [SecuredOperation("owner")]
        [ValidationAspect(typeof(OperationClaimValidator))]
        [SmartCacheRemoveAspectAsync("OperationClaim")]
        public async Task<IResult> UpdateAsync(OperationClaim operationClaim, CancellationToken ct = default)
        {
            await _operationClaimDal.UpdateAsync(operationClaim, ct);
            return new SuccessResult(Messages.OperationClaimUpdated);
        }

        [LogAspect] // Loglama eklendi
        [CacheAspect(86400)] // 24 saat cache - Rol adına göre arama
        public async Task<IDataResult<OperationClaim>> GetByNameAsync(string name, CancellationToken ct = default)
        {
            return await _operationClaimDal.GetOperationClaimByNameWithValidationAsync(name, ct);
        }
    }
}