using Entities.DTOs;
using FluentValidation;

namespace Business.ValidationRules.FluentValidation
{
    public class ExerciseCategoryAddValidator : AbstractValidator<ExerciseCategoryAddDto>
    {
        public ExerciseCategoryAddValidator()
        {
            RuleFor(x => x.CategoryName)
                .NotEmpty().WithMessage("Kategori adı boş bırakılamaz.")
                .MinimumLength(2).WithMessage("Kategori adı en az 2 karakter olmalıdır.")
                .MaximumLength(100).WithMessage("Kategori adı en fazla 100 karakter olabilir.");

            RuleFor(x => x.Description)
                .MaximumLength(500).WithMessage("Açıklama en fazla 500 karakter olabilir.");
        }
    }

    public class ExerciseCategoryUpdateValidator : AbstractValidator<ExerciseCategoryUpdateDto>
    {
        public ExerciseCategoryUpdateValidator()
        {
            RuleFor(x => x.ExerciseCategoryID)
                .GreaterThan(0).WithMessage("Geçerli bir kategori ID'si gereklidir.");

            RuleFor(x => x.CategoryName)
                .NotEmpty().WithMessage("Kategori adı boş bırakılamaz.")
                .MinimumLength(2).WithMessage("Kategori adı en az 2 karakter olmalıdır.")
                .MaximumLength(100).WithMessage("Kategori adı en fazla 100 karakter olabilir.");

            RuleFor(x => x.Description)
                .MaximumLength(500).WithMessage("Açıklama en fazla 500 karakter olabilir.");
        }
    }
}

