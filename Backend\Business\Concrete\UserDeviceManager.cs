﻿using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Caching;
using Core.Entities.Concrete;
using Core.Utilities.Results;
using DataAccess.Abstract;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Business.Concrete
{
    public class UserDeviceManager : IUserDeviceService
    {
        private readonly IUserDeviceDal _userDeviceDal;

        public UserDeviceManager(IUserDeviceDal userDeviceDal)
        {
            _userDeviceDal = userDeviceDal;
        }


        // ASYNC versiyonlar
        [PerformanceAspect(3)]
        public async Task<IDataResult<List<UserDevice>>> GetActiveDevicesByUserIdAsync(int userId, CancellationToken ct = default)
        {
            var devices = await _userDeviceDal.GetActiveDevicesByUserIdAsync(userId, ct);
            return new SuccessDataResult<List<UserDevice>>(devices);
        }

        [PerformanceAspect(3)]
        public async Task<IDataResult<UserDevice>> GetByRefreshTokenAsync(string refreshToken, CancellationToken ct = default)
        {
            return await _userDeviceDal.GetByRefreshTokenWithValidationAsync(refreshToken, ct);
        }

        [PerformanceAspect(3)]
        public async Task<IResult> AddAsync(UserDevice device, CancellationToken ct = default)
        {
            return await _userDeviceDal.AddDeviceWithManagementAsync(device, ct);
        }

        [PerformanceAspect(3)]
        public async Task<IResult> UpdateAsync(UserDevice device, CancellationToken ct = default)
        {
            await _userDeviceDal.UpdateAsync(device, ct);
            return new SuccessResult();
        }

        [PerformanceAspect(3)]
        public async Task<IResult> DeleteAsync(int id, CancellationToken ct = default)
        {
            var device = await _userDeviceDal.GetAsync(d => d.Id == id, ct);
            if (device == null)
                return new ErrorResult(Messages.DeviceNotFound);

            await _userDeviceDal.DeleteAsync(id, ct);
            return new SuccessResult();
        }

        [PerformanceAspect(3)]
        public async Task<IResult> RevokeDeviceAsync(int deviceId, CancellationToken ct = default)
        {
            return await _userDeviceDal.RevokeDeviceWithValidationAsync(deviceId, ct);
        }

        [PerformanceAspect(3)]
        public async Task<IResult> RevokeAllDevicesExceptCurrentAsync(int userId, string currentRefreshToken, CancellationToken ct = default)
        {
            return await _userDeviceDal.RevokeAllDevicesExceptCurrentAsync(userId, currentRefreshToken, ct);
        }
    }
}
