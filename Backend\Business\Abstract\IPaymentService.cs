﻿using Core.Utilities.Paging;
using Core.Utilities.Results;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Business.Abstract
{
    public interface IPaymentService
    {
        // Async yüzeyler (CT'li)
        Task<IDataResult<List<Payment>>> GetAllAsync(CancellationToken ct = default);
        Task<IResult> AddAsync(Payment payment, CancellationToken ct = default);
        Task<IResult> UpdateAsync(Payment payment, CancellationToken ct = default);
        Task<IResult> DeleteAsync(int id, CancellationToken ct = default);
        Task<IDataResult<List<PaymentHistoryDto>>> GetPaymentHistoryAsync(CancellationToken ct = default);
        Task<IDataResult<List<PaymentHistoryDto>>> GetDebtorMembersAsync(CancellationToken ct = default);
        Task<IResult> UpdatePaymentStatusAsync(int paymentId, string paymentMethod, CancellationToken ct = default);
        Task<IDataResult<PaginatedResult<PaymentHistoryDto>>> GetPaymentHistoryPaginatedAsync(PaymentPagingParameters parameters, CancellationToken ct = default);
        Task<IDataResult<PaymentTotals>> GetPaymentTotalsAsync(PaymentPagingParameters parameters, CancellationToken ct = default);
        Task<IDataResult<MonthlyRevenueDto>> GetMonthlyRevenueAsync(int year, CancellationToken ct = default);
        Task<IDataResult<List<PaymentHistoryDto>>> GetAllPaymentHistoryFilteredAsync(PaymentPagingParameters parameters, CancellationToken ct = default);
    }
}
