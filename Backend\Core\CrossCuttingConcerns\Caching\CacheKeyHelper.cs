using System;
using System.Collections.Generic;
using System.Linq;

namespace Core.CrossCuttingConcerns.Caching
{
    /// <summary>
    /// Multi-tenant cache key generation ve management utility
    /// Hierarchical key structure: "gym:{companyId}:{entity}:{id}"
    /// </summary>
    public static class CacheKeyHelper
    {
        private const string ROOT_PREFIX = "gym";
        private const string SEPARATOR = ":";

        /// <summary>
        /// Hierarchical cache key oluşturur
        /// Format: "gym:{companyId}:{entity}:{id}"
        /// </summary>
        /// <param name="companyId">Company ID (multi-tenant isolation için)</param>
        /// <param name="entity">Entity adı (member, payment, membership vb.)</param>
        /// <param name="identifier">Unique identifier (details, list, id vb.)</param>
        /// <param name="additionalParts">Ek key parçaları (opsiyonel)</param>
        /// <returns>Hierarchical cache key</returns>
        public static string GenerateKey(int companyId, string entity, string identifier, params string[] additionalParts)
        {
            if (companyId <= 0)
                throw new ArgumentException("CompanyId must be greater than 0", nameof(companyId));
            
            if (string.IsNullOrWhiteSpace(entity))
                throw new ArgumentException("Entity cannot be null or empty", nameof(entity));
            
            if (string.IsNullOrWhiteSpace(identifier))
                throw new ArgumentException("Identifier cannot be null or empty", nameof(identifier));

            var keyParts = new List<string>
            {
                ROOT_PREFIX,
                companyId.ToString(),
                entity.ToLowerInvariant(),
                identifier.ToLowerInvariant()
            };

            // Ek parçalar varsa ekle
            if (additionalParts != null && additionalParts.Length > 0)
            {
                keyParts.AddRange(additionalParts.Where(p => !string.IsNullOrWhiteSpace(p))
                                                .Select(p => p.ToLowerInvariant()));
            }

            return string.Join(SEPARATOR, keyParts);
        }

        /// <summary>
        /// Company bazlı pattern oluşturur
        /// Format: "gym:{companyId}:*"
        /// </summary>
        /// <param name="companyId">Company ID</param>
        /// <returns>Company pattern</returns>
        public static string GetCompanyPattern(int companyId)
        {
            if (companyId <= 0)
                throw new ArgumentException("CompanyId must be greater than 0", nameof(companyId));

            return $"{ROOT_PREFIX}{SEPARATOR}{companyId}{SEPARATOR}*";
        }

        /// <summary>
        /// Entity bazlı pattern oluşturur
        /// Format: "gym:{companyId}:{entity}:*"
        /// </summary>
        /// <param name="companyId">Company ID</param>
        /// <param name="entity">Entity adı</param>
        /// <returns>Entity pattern</returns>
        public static string GetEntityPattern(int companyId, string entity)
        {
            if (companyId <= 0)
                throw new ArgumentException("CompanyId must be greater than 0", nameof(companyId));
            
            if (string.IsNullOrWhiteSpace(entity))
                throw new ArgumentException("Entity cannot be null or empty", nameof(entity));

            return $"{ROOT_PREFIX}{SEPARATOR}{companyId}{SEPARATOR}{entity.ToLowerInvariant()}{SEPARATOR}*";
        }

        /// <summary>
        /// Cache key'den company ID'yi çıkarır
        /// </summary>
        /// <param name="cacheKey">Cache key</param>
        /// <returns>Company ID veya -1 (geçersiz key)</returns>
        public static int ExtractCompanyId(string cacheKey)
        {
            if (string.IsNullOrWhiteSpace(cacheKey))
                return -1;

            var parts = cacheKey.Split(SEPARATOR);
            if (parts.Length < 3 || parts[0] != ROOT_PREFIX)
                return -1;

            if (int.TryParse(parts[1], out int companyId))
                return companyId;

            return -1;
        }

        /// <summary>
        /// Cache key'den entity adını çıkarır
        /// </summary>
        /// <param name="cacheKey">Cache key</param>
        /// <returns>Entity adı veya null (geçersiz key)</returns>
        public static string ExtractEntity(string cacheKey)
        {
            if (string.IsNullOrWhiteSpace(cacheKey))
                return null;

            var parts = cacheKey.Split(SEPARATOR);
            if (parts.Length < 4 || parts[0] != ROOT_PREFIX)
                return null;

            return parts[2];
        }

        /// <summary>
        /// Cache key'in geçerli format'ta olup olmadığını kontrol eder
        /// </summary>
        /// <param name="cacheKey">Cache key</param>
        /// <returns>True: geçerli, False: geçersiz</returns>
        public static bool IsValidKey(string cacheKey)
        {
            if (string.IsNullOrWhiteSpace(cacheKey))
                return false;

            var parts = cacheKey.Split(SEPARATOR);
            
            // Minimum 4 parça olmalı: gym:companyId:entity:identifier
            if (parts.Length < 4)
                return false;

            // İlk parça "gym" olmalı
            if (parts[0] != ROOT_PREFIX)
                return false;

            // İkinci parça geçerli bir sayı olmalı (companyId)
            if (!int.TryParse(parts[1], out int companyId) || companyId <= 0)
                return false;

            // Üçüncü ve dördüncü parça boş olmamalı
            if (string.IsNullOrWhiteSpace(parts[2]) || string.IsNullOrWhiteSpace(parts[3]))
                return false;

            return true;
        }

        /// <summary>
        /// Predefined entity cache key'leri için helper method'lar
        /// </summary>
        public static class Entities
        {
            public static string Member(int companyId, string action = "details")
                => GenerateKey(companyId, "member", action);

            public static string MembershipType(int companyId, string action = "list")
                => GenerateKey(companyId, "membershiptype", action);

            public static string Payment(int companyId, string action, params string[] additionalParts)
                => GenerateKey(companyId, "payment", action, additionalParts);

            public static string License(int companyId, string action = "packages")
                => GenerateKey(companyId, "license", action);

            public static string Branch(int companyId, string action = "list")
                => GenerateKey(companyId, "branch", action);

            public static string User(int companyId, string action, params string[] additionalParts)
                => GenerateKey(companyId, "user", action, additionalParts);
        }

        /// <summary>
        /// Cache expiration policies (entity bazlı)
        /// </summary>
        public static class ExpirationPolicies
        {
            public static TimeSpan Member => TimeSpan.FromMinutes(5);
            public static TimeSpan MembershipType => TimeSpan.FromMinutes(30);
            public static TimeSpan Payment => TimeSpan.FromMinutes(10);
            public static TimeSpan License => TimeSpan.FromHours(1);
            public static TimeSpan Branch => TimeSpan.FromHours(2);
            public static TimeSpan User => TimeSpan.FromMinutes(15);
            public static TimeSpan Default => TimeSpan.FromMinutes(10);

            public static TimeSpan GetExpiration(string entity)
            {
                return entity?.ToLowerInvariant() switch
                {
                    "member" => Member,
                    "membershiptype" => MembershipType,
                    "payment" => Payment,
                    "license" => License,
                    "branch" => Branch,
                    "user" => User,
                    _ => Default
                };
            }
        }
    }
}
