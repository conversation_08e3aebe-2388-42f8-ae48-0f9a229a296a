using Core.Utilities.Results;
using Entities.DTOs;

namespace Business.Abstract
{
    public interface IQrCodeEncryptionService
    {
        /// <summary>
        /// QR kod için şifreli token oluşturur
        /// </summary>
        /// <param name="memberId">Üye ID</param>
        /// <param name="scanNumber">Scan Number</param>
        /// <returns>Şifreli QR kod token'ı</returns>
        string CreateEncryptedQrToken(int memberId, string scanNumber);

        /// <summary>
        /// Şifreli QR kod token'ını çözer
        /// </summary>
        /// <param name="encryptedToken">Şifreli token</param>
        /// <returns>Çözülmüş QR kod bilgileri</returns>
        IDataResult<DecryptedQrCodeDto> DecryptQrToken(string encryptedToken);

        /// <summary>
        /// QR kod token'ının geçerliliğini kontrol eder
        /// </summary>
        /// <param name="timestamp">Unix timestamp</param>
        /// <param name="validityMinutes">Geçerlilik süresi (dakika)</param>
        /// <returns>Geçerli mi?</returns>
        bool IsTokenValid(long timestamp, int validityMinutes = 5);
    }
}
