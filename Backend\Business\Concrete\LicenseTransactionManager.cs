﻿using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using System.Threading;
using System.Threading.Tasks;

using System.Collections.Generic;

namespace Business.Concrete
{
    public class LicenseTransactionManager : ILicenseTransactionService
    {
        private readonly ILicenseTransactionDal _licenseTransactionDal;

        public LicenseTransactionManager(ILicenseTransactionDal licenseTransactionDal)
        {
            _licenseTransactionDal = licenseTransactionDal;
        }

	        // ASYNC UYGULAMALAR
	        [SecuredOperation("owner")]
	        [LogAspect]
	        [PerformanceAspect(3)]
	        [SmartCacheRemoveAspectAsync("LicenseTransaction")]
	        public async Task<IResult> AddAsync(LicenseTransaction licenseTransaction, CancellationToken ct = default)
	        {
	            return await _licenseTransactionDal.AddLicenseTransactionAsync(licenseTransaction, ct);
	        }

	        [SecuredOperation("owner")]
	        [PerformanceAspect(3)]
	        [CacheAspect(1800)]
	        public async Task<IDataResult<List<LicenseTransaction>>> GetAllAsync(CancellationToken ct = default)
	        {
	            return await _licenseTransactionDal.GetAllOrderedByDateAsync(ct);
	        }

	        [SecuredOperation("owner")]
	        [PerformanceAspect(3)]
	        [CacheAspect(600)]
	        public async Task<IDataResult<List<LicenseTransaction>>> GetAllFilteredAsync(int? userID, string startDate, string endDate, int page, int pageSize, CancellationToken ct = default)
	        {
	            return await _licenseTransactionDal.GetAllFilteredAsync(userID, startDate, endDate, page, pageSize, ct);
	        }

	        [SecuredOperation("owner")]
	        [PerformanceAspect(3)]
	        [CacheAspect(1800)]
	        public async Task<IDataResult<List<LicenseTransaction>>> GetByUserIdAsync(int userId, CancellationToken ct = default)
	        {
	            var data = await _licenseTransactionDal.GetAllAsync(lt => lt.UserID == userId && lt.IsActive == true, ct);
	            return new SuccessDataResult<List<LicenseTransaction>>(data);
	        }

	        [SecuredOperation("owner")]
	        [LogAspect]
	        [PerformanceAspect(3)]
	        [SmartCacheRemoveAspectAsync("LicenseTransaction")]
	        public async Task<IResult> DeleteAsync(int id, CancellationToken ct = default)
	        {
	            return await _licenseTransactionDal.SoftDeleteLicenseTransactionAsync(id, ct);
	        }

	        [SecuredOperation("owner")]
	        [PerformanceAspect(3)]
	        [CacheAspect(1800)]
	        public async Task<IDataResult<object>> GetTotalsAsync(CancellationToken ct = default)
	        {
	            return await _licenseTransactionDal.GetTotalAmountsByPaymentMethodAsync(ct);
	        }

	        [SecuredOperation("owner")]
	        [PerformanceAspect(3)]
	        [CacheAspect(1800)]
	        public async Task<IDataResult<object>> GetMonthlyRevenueAsync(int year, CancellationToken ct = default)
	        {
	            return await _licenseTransactionDal.GetMonthlyRevenueByYearAsync(year, ct);
	        }
    }
}