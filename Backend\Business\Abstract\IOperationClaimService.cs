﻿using Core.Entities.Concrete;
using Core.Utilities.Results;
using Entities.Concrete;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Business.Abstract
{
    public interface IOperationClaimService
    {
        // Async
        Task<IDataResult<List<OperationClaim>>> GetAllAsync(CancellationToken ct = default);
        Task<IDataResult<OperationClaim>> GetByIdAsync(int id, CancellationToken ct = default);
        Task<IResult> AddAsync(OperationClaim operationClaim, CancellationToken ct = default);
        Task<IResult> UpdateAsync(OperationClaim operationClaim, CancellationToken ct = default);
        Task<IResult> DeleteAsync(int id, CancellationToken ct = default);
        Task<IDataResult<OperationClaim>> GetByNameAsync(string name, CancellationToken ct = default);
    }
}
