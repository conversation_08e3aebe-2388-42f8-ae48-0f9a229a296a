using Castle.DynamicProxy;
using Core.CrossCuttingConcerns.Caching;
using Core.Utilities.Interceptors;
using Core.Utilities.IoC;
using Core.Utilities.Security.CompanyContext;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using Core.CrossCuttingConcerns.Logging;
using Microsoft.AspNetCore.Http;


using System;
using System.Diagnostics;
using System.Globalization;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using System.Security.Cryptography;
using System.Threading;
using System.Collections.Concurrent;
using StackExchange.Redis;

namespace Core.Aspects.Autofac.Caching
{
    /// <summary>
    /// AOP Cache Aspect - Method'lara otomatik cache ekleme
    /// Multi-tenant aware, parameter-based key generation
    /// Performance monitoring ve cache hit/miss logging
    /// </summary>
    public class CacheAspect : MethodInterceptionAsync
    {
        private readonly int _duration;
        private readonly ICacheService _cacheService;
        private readonly ICompanyContext _companyContext;
        private readonly ILogService _logService;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly string _environmentName;

        // Single-flight pattern için concurrent dictionary
        private static readonly ConcurrentDictionary<string, SemaphoreSlim> _lockDictionary = new();
        private static readonly object _lockCleanupLock = new object();

        // Lazy cleanup için timer (5 dakikada bir çalışır)
        private static readonly Timer _cleanupTimer = new Timer(PerformLazyCleanup, null,
            TimeSpan.FromMinutes(5), TimeSpan.FromMinutes(5));

        /// <summary>
        /// Cache aspect constructor
        /// </summary>
        /// <param name="duration">Cache süresi (saniye)</param>
        public CacheAspect(int duration)
        {
            _duration = duration;
            _cacheService = ServiceTool.ServiceProvider.GetService<ICacheService>();
            _companyContext = ServiceTool.ServiceProvider.GetService<ICompanyContext>();
            _logService = ServiceTool.ServiceProvider.GetService<ILogService>();
            _httpContextAccessor = ServiceTool.ServiceProvider.GetService<IHttpContextAccessor>();
            _environmentName = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT")
                                 ?? Environment.GetEnvironmentVariable("DOTNET_ENVIRONMENT")
                                 ?? "Production";
            Priority = 5; // Performance'dan sonra - Cache okuma/yazma
        }

        public override void Intercept(IInvocation invocation)
        {
            var methodName = $"{invocation.Method.ReflectedType.FullName}.{invocation.Method.Name}";
            var cacheKey = GenerateCacheKey(invocation);
            var returnType = invocation.Method.ReturnType;

            var sw = Stopwatch.StartNew();

            try
            {
                // ASYNC: Task<T> dönüşleri ele al - CACHE-FIRST APPROACH
                if (returnType.IsGenericType && returnType.GetGenericTypeDefinition() == typeof(Task<>))
                {
                    var resultType = returnType.GetGenericArguments()[0];

                    // Cache-first: Önce cache kontrol et, sonra Proceed() çağır
                    invocation.ReturnValue = ExecuteWithCacheAsync(invocation, cacheKey, resultType, methodName, sw);

                    return;
                }

                // ASYNC: Task (void) dönüşleri – cache uygulanmaz
                if (returnType == typeof(Task))
                {
                    invocation.Proceed();
                    var originalTask = (Task)invocation.ReturnValue;
                    invocation.ReturnValue = WrapTaskNoResult(originalTask);
                    sw.Stop();
                    return;
                }

                // NON-ASYNC: CacheAspect yalnızca async metotları destekler → bypass
                invocation.Proceed();
                sw.Stop();
                return;
            }
            catch (Exception ex)
            {
                sw.Stop();
                LogCacheError(methodName, cacheKey, ex);
                throw;
            }
        }

        /// <summary>
        /// Method signature ve parametrelerine göre cache key oluşturur
        /// Format: "gym:{companyId}:{className}:{methodName}:{parameterHash}"
        /// </summary>
        private string GenerateCacheKey(IInvocation invocation)
        {
            try
            {
                var companyId = _companyContext.GetCompanyId();

                // Interface adından entity adını çıkar
                // IMemberService -> imemberservice (mevcut sistem)
                // MemberManager -> member (alternatif)
                var typeName = invocation.Method.ReflectedType.Name;
                var className = typeName.Replace("Manager", "").ToLowerInvariant();

                var methodName = invocation.Method.Name.ToLowerInvariant();

                // Parameter hash oluştur
                var parameterHash = GenerateParameterHash(invocation);

                // CacheKeyHelper kullanarak hierarchical key oluştur
                // Format: gym:{companyId}:{entity}:{action}:{additionalParts}
                return CacheKeyHelper.GenerateKey(companyId, className, methodName, parameterHash);
            }
            catch (Exception ex)
            {
                // Fallback key generation - CompanyId is always valid (starts from 1)
                var fallbackKey = $"gym:{_companyContext.GetCompanyId()}:cache_error:{invocation.Method.Name}:{DateTime.UtcNow.Ticks}";
                LogCacheError($"{invocation.Method.ReflectedType.FullName}.{invocation.Method.Name}",
                             fallbackKey, ex);
                return fallbackKey;
            }
        }

        /// <summary>
        /// Method parametrelerinden deterministik hash oluşturur
        /// CancellationToken parametrelerini filtreler, SHA256 kullanır
        /// </summary>
        private string GenerateParameterHash(IInvocation invocation)
        {
            if (invocation.Arguments == null || invocation.Arguments.Length == 0)
            {
                return "noparams";
            }

            var paramBuilder = new StringBuilder();
            var parameters = invocation.Method.GetParameters();
            var validParamCount = 0;

            for (int i = 0; i < invocation.Arguments.Length; i++)
            {
                var arg = invocation.Arguments[i];
                var paramType = parameters[i].ParameterType;
                var paramName = parameters[i].Name;

                // CancellationToken parametrelerini cache key'den çıkar
                if (paramType == typeof(CancellationToken) || paramType == typeof(CancellationToken?))
                {
                    continue;
                }

                if (validParamCount > 0)
                    paramBuilder.Append("_");

                if (arg == null)
                {
                    paramBuilder.Append($"{paramName}:null");
                }
                else if (IsPrimitiveType(arg.GetType()))
                {
                    // Culture-invariant formatting for deterministic cache keys
                    var formattedValue = FormatPrimitiveValue(arg);
                    paramBuilder.Append($"{paramName}:{formattedValue}");
                }
                else
                {
                    // Complex object için deterministik JSON hash
                    try
                    {
                        var json = JsonConvert.SerializeObject(arg, new JsonSerializerSettings
                        {
                            Formatting = Formatting.None,
                            DateFormatHandling = DateFormatHandling.IsoDateFormat,
                            DateTimeZoneHandling = DateTimeZoneHandling.Utc,
                            NullValueHandling = NullValueHandling.Include,
                            DefaultValueHandling = DefaultValueHandling.Include
                        });

                        var hash = ComputeDeterministicHash(json);
                        paramBuilder.Append($"{paramName}:{hash}");
                    }
                    catch
                    {
                        paramBuilder.Append($"{paramName}:{arg.GetType().Name}");
                    }
                }

                validParamCount++;
            }

            return paramBuilder.ToString().ToLowerInvariant();
        }

        /// <summary>
        /// SHA256 kullanarak deterministik hash oluşturur
        /// </summary>
        private string ComputeDeterministicHash(string input)
        {
            if (string.IsNullOrEmpty(input))
                return "empty";

            using (var sha256 = SHA256.Create())
            {
                var bytes = Encoding.UTF8.GetBytes(input);
                var hashBytes = sha256.ComputeHash(bytes);
                return Convert.ToHexString(hashBytes)[..16]; // İlk 16 karakter (64 bit)
            }
        }

        /// <summary>
        /// Primitive type kontrolü
        /// </summary>
        private bool IsPrimitiveType(Type type)
        {
            return type.IsPrimitive ||
                   type == typeof(string) ||
                   type == typeof(DateTime) ||
                   type == typeof(decimal) ||
                   type == typeof(Guid) ||
                   type.IsEnum;
        }

        /// <summary>
        /// Culture-invariant primitive value formatting for deterministic cache keys
        /// </summary>
        private string FormatPrimitiveValue(object value)
        {
            return value switch
            {
                DateTime dt => dt.ToString("yyyy-MM-ddTHH:mm:ss.fffZ", CultureInfo.InvariantCulture),
                decimal dec => dec.ToString("G", CultureInfo.InvariantCulture),
                double dbl => dbl.ToString("G", CultureInfo.InvariantCulture),
                float flt => flt.ToString("G", CultureInfo.InvariantCulture),
                _ => value.ToString() ?? string.Empty
            };
        }



        /// <summary>
        /// Cache hit logging - Production optimized
        /// </summary>
        private void LogCacheHit(string methodName, string cacheKey, long elapsedMs)
        {
            if (!IsDevelopment()) return;

            try
            {
                _logService?.Info($"[CACHE HIT] {methodName} | {elapsedMs}ms | Company: {_companyContext?.GetCompanyId()}");
            }
            catch { }
        }

        /// <summary>
        /// Cache miss logging - Production optimized
        /// </summary>
        private void LogCacheMiss(string methodName, string cacheKey, long elapsedMs)
        {
            if (!IsDevelopment()) return;

            try
            {
                _logService?.Info($"[CACHE MISS] {methodName} | {elapsedMs}ms | Company: {_companyContext?.GetCompanyId()}");
            }
            catch { }
        }

        /// <summary>
        /// Cache error logging - Production optimized
        /// </summary>
        private void LogCacheError(string methodName, string cacheKey, Exception ex)
        {
            try
            {
                _logService?.Error($"[CACHE ERROR] {methodName} | Error: {ex.Message} | Company: {_companyContext?.GetCompanyId()}");
            }
            catch { }
        }

        private string GetCurrentUser()
        {
            try
            {
                var user = _httpContextAccessor?.HttpContext?.User;
                if (user == null) return "Anonymous";
                var userIdClaim = user.FindFirst("UserID") ?? user.FindFirst("sub") ?? user.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier);
                if (userIdClaim != null && !string.IsNullOrEmpty(userIdClaim.Value))
                    return userIdClaim.Value;
                var username = user.Identity?.Name;
                return string.IsNullOrEmpty(username) ? "Anonymous" : username;
            }
            catch { return "Anonymous"; }
        }

        private bool IsDevelopment()
        {
            try { return string.Equals(_environmentName, "Development", StringComparison.OrdinalIgnoreCase); } catch { return false; }
        }


        /// <summary>
        /// Type-safe cache value retrieval - ASYNC VERSION
        /// </summary>
        private async Task<object> GetCachedValueAsync(string cacheKey, Type returnType)
        {
            try
            {
                if (IsDevelopment())
                    Console.WriteLine($"[CACHE DEBUG] GetCachedValueAsync - Key: {cacheKey}, Type: {returnType.FullName}");

                // Async cache get - deadlock-safe
                var stringValue = await _cacheService.GetAsync<string>(cacheKey).ConfigureAwait(false);
                if (IsDevelopment())
                    Console.WriteLine($"[CACHE DEBUG] String Value: {(stringValue != null ? "Found" : "Not Found")}");

                if (stringValue == null)
                    return null;

                // JSON'dan deserialize et - Interface type'lar için concrete type kullan
                var result = DeserializeWithConcreteType(stringValue, returnType);

                if (IsDevelopment())
                    Console.WriteLine($"[CACHE DEBUG] GetCachedValueAsync Result: {(result != null ? "Found" : "Not Found")}");
                return result;
            }
            catch (Exception ex)
            {
                if (IsDevelopment())
                    Console.WriteLine($"[CACHE DEBUG] GetCachedValueAsync Error: {ex.Message}");
                LogCacheError("GetCachedValueAsync", cacheKey, ex);
                return null;
            }
        }


        /// <summary>
        /// Type-safe cache value storage (async)
        /// </summary>
        private async Task SetCachedValueAsync(string cacheKey, object value, Type valueType, TimeSpan expiry)
        {
            try
            {
                var settings = new JsonSerializerSettings
                {
                    TypeNameHandling = TypeNameHandling.Auto,
                    NullValueHandling = NullValueHandling.Include
                };
                var jsonValue = JsonConvert.SerializeObject(value, settings);
                await _cacheService.SetAsync(cacheKey, jsonValue, expiry).ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                LogCacheError("SetCachedValueAsync", cacheKey, ex);
            }
        }

        /// <summary>
        /// Interface type'lar için concrete type ile deserialization
        /// </summary>
        private object DeserializeWithConcreteType(string jsonValue, Type returnType)
        {
            try
            {

                // IDataResult<T> interface'i için manuel deserialization
                if (returnType.IsGenericType && returnType.GetGenericTypeDefinition() == typeof(Core.Utilities.Results.IDataResult<>))
                {
                    var genericArg = returnType.GetGenericArguments()[0];

                    // JSON'u JObject olarak parse et
                    if (string.IsNullOrEmpty(jsonValue))
                        return null;

                    var jsonObject = Newtonsoft.Json.Linq.JObject.Parse(jsonValue);

                    // Data, Success, Message'ı ayrı ayrı çıkar
                    var dataToken = jsonObject["Data"];
                    var successToken = jsonObject["Success"];
                    var messageToken = jsonObject["Message"];



                    // Data'yı generic type'a deserialize et
                    var data = dataToken != null && dataToken.Type != Newtonsoft.Json.Linq.JTokenType.Null
                        ? dataToken.ToObject(genericArg)
                        : null;

                    var success = successToken?.ToObject<bool>() ?? true;
                    var message = messageToken?.ToObject<string>();

                    // Success durumuna göre SuccessDataResult veya ErrorDataResult oluştur
                    Type concreteType;
                    if (success)
                    {
                        concreteType = typeof(Core.Utilities.Results.SuccessDataResult<>).MakeGenericType(genericArg);
                    }
                    else
                    {
                        concreteType = typeof(Core.Utilities.Results.ErrorDataResult<>).MakeGenericType(genericArg);
                    }

                    object? result;
                    if (!string.IsNullOrEmpty(message))
                    {
                        // (T data, string message) constructor
                        result = Activator.CreateInstance(concreteType, data, message);
                    }
                    else
                    {
                        // (T data) constructor
                        result = Activator.CreateInstance(concreteType, data);
                    }

                    return result;
                }

                // Diğer interface'ler için TypeNameHandling ile deserialization
                if (string.IsNullOrEmpty(jsonValue))
                    return null;

                var settings = new JsonSerializerSettings
                {
                    TypeNameHandling = TypeNameHandling.Auto,
                    NullValueHandling = NullValueHandling.Include
                };
                return JsonConvert.DeserializeObject(jsonValue, returnType, settings);
            }
            catch (JsonException ex)
            {
                // Cache corruption: Log and return null (cache miss behavior)
                LogCacheError("Cache deserialize failed - treating as miss", jsonValue, ex);
                return null;
            }
            catch (Exception ex)
            {
                // Unexpected error: Log and return null
                LogCacheError("Unexpected cache error - treating as miss", jsonValue, ex);
                return null;
            }
        }



        /// <summary>
        /// Single-flight pattern için lock alır
        /// </summary>
        private SemaphoreSlim GetOrCreateLock(string cacheKey)
        {
            return _lockDictionary.GetOrAdd(cacheKey, _ => new SemaphoreSlim(1, 1));
        }

        /// <summary>
        /// Lock cleanup - memory leak önleme (race condition safe)
        /// </summary>
        private void CleanupLock(string cacheKey)
        {
            lock (_lockCleanupLock)
            {
                if (_lockDictionary.TryGetValue(cacheKey, out var semaphore))
                {
                    // Güvenli cleanup: Sadece available durumda ve kimse beklemiyorsa temizle
                    if (semaphore.CurrentCount == 1 && semaphore.WaitAsync(0).IsCompleted)
                    {
                        try
                        {
                            // Double-check: Hala dictionary'de mi?
                            if (_lockDictionary.TryRemove(cacheKey, out var removedSemaphore) &&
                                ReferenceEquals(semaphore, removedSemaphore))
                            {
                                semaphore.Dispose();
                            }
                        }
                        catch
                        {
                            // Dispose sırasında hata olursa sessizce geç
                            // Semaphore zaten dictionary'den çıkarıldı
                        }
                    }
                }
            }
        }

        /// <summary>
        /// Lazy cleanup - 5 dakikada bir çalışır, kullanılmayan semaphore'ları temizler
        /// </summary>
        private static void PerformLazyCleanup(object state)
        {
            var keysToRemove = new List<string>();

            lock (_lockCleanupLock)
            {
                foreach (var kvp in _lockDictionary)
                {
                    var semaphore = kvp.Value;

                    // Sadece available ve kimse beklemiyor ise temizle
                    if (semaphore.CurrentCount == 1)
                    {
                        try
                        {
                            // Non-blocking check: Hemen alabilir miyiz?
                            if (semaphore.WaitAsync(0).IsCompleted)
                            {
                                keysToRemove.Add(kvp.Key);
                                semaphore.Release(); // WaitAsync(0) ile aldığımızı geri ver
                            }
                        }
                        catch
                        {
                            // Hata durumunda key'i temizleme listesine ekle
                            keysToRemove.Add(kvp.Key);
                        }
                    }
                }

                // Temizlenecek key'leri kaldır
                foreach (var key in keysToRemove)
                {
                    if (_lockDictionary.TryRemove(key, out var semaphore))
                    {
                        try
                        {
                            semaphore.Dispose();
                        }
                        catch
                        {
                            // Dispose hatası sessizce geç
                        }
                    }
                }
            }
        }

        /// <summary>
        /// Cache execution wrapper - reflection ile generic method çağırır
        /// </summary>
        private object ExecuteWithCacheAsync(IInvocation invocation, string cacheKey, Type resultType, string methodName, Stopwatch sw)
        {
            var method = typeof(CacheAspect)
                .GetMethod(nameof(WrapTaskWithCacheFirstAsync), BindingFlags.NonPublic | BindingFlags.Instance)
                .MakeGenericMethod(resultType);

            return method.Invoke(this, new object[] { invocation, cacheKey, resultType, methodName, sw });
        }

        /// <summary>
        /// Cache-first approach: Önce cache kontrol et, miss'te Proceed() çağır
        /// </summary>
        private async Task<T> WrapTaskWithCacheFirstAsync<T>(IInvocation invocation, string cacheKey, Type valueType, string methodName, System.Diagnostics.Stopwatch sw)
        {
            // İlk cache kontrolü - lock almadan
            var cached = await GetCachedValueAsync(cacheKey, valueType).ConfigureAwait(false);
            if (cached != null)
            {
                sw.Stop();
                LogCacheHit(methodName, cacheKey, sw.ElapsedMilliseconds);
                return (T)cached;
            }

            // Cache miss - single-flight pattern
            var lockKey = $"lock:{cacheKey}";
            var semaphore = GetOrCreateLock(lockKey);

            try
            {
                // 30 saniye timeout ile lock al
                var lockAcquired = await semaphore.WaitAsync(TimeSpan.FromSeconds(30)).ConfigureAwait(false);
                if (!lockAcquired)
                {
                    // Timeout durumunda orijinal metodu çalıştır
                    var timeoutResult = await ExecuteOriginalMethodAsync<T>(invocation).ConfigureAwait(false);
                    sw.Stop();
                    LogCacheMiss(methodName, cacheKey, sw.ElapsedMilliseconds);
                    return timeoutResult;
                }

                try
                {
                    // Lock aldıktan sonra tekrar cache kontrol et
                    cached = await GetCachedValueAsync(cacheKey, valueType).ConfigureAwait(false);
                    if (cached != null)
                    {
                        sw.Stop();
                        LogCacheHit(methodName, cacheKey, sw.ElapsedMilliseconds);
                        return (T)cached;
                    }

                    // Hala cache miss - orijinal metodu çalıştır
                    var result = await ExecuteOriginalMethodAsync<T>(invocation).ConfigureAwait(false);

                    sw.Stop();
                    LogCacheMiss(methodName, cacheKey, sw.ElapsedMilliseconds);

                    // Sonucu cache'e yaz
                    if (result != null)
                    {
                        try
                        {
                            var expiry = TimeSpan.FromSeconds(_duration);
                            await SetCachedValueAsync(cacheKey, result, valueType, expiry).ConfigureAwait(false);
                        }
                        catch
                        {
                            // Cache yazma hatası sessizce geç
                        }
                    }

                    return result;
                }
                finally
                {
                    semaphore.Release();
                }
            }
            catch (Exception ex)
            {
                sw.Stop();
                LogCacheError(methodName, cacheKey, ex);
                throw;
            }
            finally
            {
                // Lock cleanup
                CleanupLock(lockKey);
            }
        }

        /// <summary>
        /// Orijinal metodu interceptor bypass ederek çalıştırır (recursion önleme)
        /// </summary>
        private async Task<T> ExecuteOriginalMethodAsync<T>(IInvocation invocation)
        {
            // Reflection ile target object'in metodunu direkt çağır
            var targetMethod = invocation.Method;
            var targetObject = invocation.InvocationTarget ?? invocation.Proxy;

            try
            {
                var result = targetMethod.Invoke(targetObject, invocation.Arguments);
                if (result is Task<T> task)
                {
                    return await task.ConfigureAwait(false);
                }
                throw new InvalidOperationException($"Method {targetMethod.Name} did not return Task<T>");
            }
            catch (TargetInvocationException ex)
            {
                // Reflection exception'ını unwrap et
                throw ex.InnerException ?? ex;
            }
        }

        // Task (void) için sarıcı: sadece tamamlanınca devam
        private async Task WrapTaskNoResult(Task task)
        {
            await task.ConfigureAwait(false);
        }



    }
}
