using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Core.CrossCuttingConcerns.Caching;
using Core.Utilities.Results;
using Core.Utilities.Security.CompanyContext;
using Entities.DTOs;

using StackExchange.Redis;
using System.Diagnostics;
using System.Threading;
using System.Threading.Tasks;
using System.Linq;
using System.Collections.Generic;

namespace Business.Concrete
{
    public class CacheAdminManager : ICacheAdminService
    {
        private readonly ICacheService _cacheService;
        private readonly ICompanyContext _companyContext;
        private readonly IConnectionMultiplexer _connectionMultiplexer;
        private readonly ICompanyService _companyService;

        public CacheAdminManager(
            ICacheService cacheService,
            ICompanyContext companyContext,
            IConnectionMultiplexer connectionMultiplexer,
            ICompanyService companyService)
        {
            _cacheService = cacheService;
            _companyContext = companyContext;
            _connectionMultiplexer = connectionMultiplexer;
            _companyService = companyService;
        }

        [SecuredOperation("owner")]
        public async Task<IDataResult<object>> GetCacheStatisticsAsync(CancellationToken ct = default)
        {
            var companyId = _companyContext.GetCompanyId();
            if (companyId <= 0)
                return new ErrorDataResult<object>("Geçersiz company ID");

            var stats = await GetCompanyCacheStatistics(companyId);
            return new SuccessDataResult<object>(stats, "Cache istatistikleri başarıyla getirildi");
        }

        [SecuredOperation("owner")]
        public async Task<IDataResult<object>> GetCacheHealthAsync(CancellationToken ct = default)
        {
            try
            {
                var stopwatch = Stopwatch.StartNew();
                var database = _connectionMultiplexer.GetDatabase();
                var pingTime = await database.PingAsync();
                stopwatch.Stop();

                var health = new
                {
                    IsConnected = _connectionMultiplexer.IsConnected,
                    PingTime = pingTime.TotalMilliseconds,
                    ResponseTime = stopwatch.ElapsedMilliseconds,
                    Status = _connectionMultiplexer.IsConnected ? "Healthy" : "Unhealthy",
                    ServerInfo = await GetRedisServerInfo()
                };

                return new SuccessDataResult<object>(health, "Cache sağlık durumu başarıyla kontrol edildi");
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<object>($"Cache sağlık kontrolü yapılırken hata oluştu: {ex.Message}");
            }
        }

        [SecuredOperation("owner")]
        public async Task<IDataResult<object>> GetCompanyCacheKeysAsync(int page = 1, int size = 50, CancellationToken ct = default)
        {
            var companyId = _companyContext.GetCompanyId();
            if (companyId <= 0)
                return new ErrorDataResult<object>("Geçersiz company ID");

            var pattern = $"gym:{companyId}:*";
            var keys = await GetKeysByPatternInternal(pattern, page, size);
            return new SuccessDataResult<object>(keys, "Cache key'leri başarıyla getirildi");
        }

        [SecuredOperation("owner")]
        public async Task<IDataResult<object>> GetKeysByPatternAsync(string pattern, int page = 1, int size = 50, CancellationToken ct = default)
        {
            var companyId = _companyContext.GetCompanyId();
            if (companyId <= 0)
                return new ErrorDataResult<object>("Geçersiz company ID");

            if (!pattern.StartsWith($"gym:{companyId}:"))
            {
                pattern = $"gym:{companyId}:{pattern.TrimStart('*')}";
            }

            var keys = await GetKeysByPatternInternal(pattern, page, size);
            return new SuccessDataResult<object>(keys, "Pattern'e göre cache key'leri başarıyla getirildi");
        }

        [SecuredOperation("owner")]
        public async Task<IDataResult<object>> ClearTenantCacheAsync(CancellationToken ct = default)
        {
            var companyId = _companyContext.GetCompanyId();
            if (companyId <= 0)
                return new ErrorDataResult<object>("Geçersiz company ID");

            var pattern = $"gym:{companyId}:*";
            var removedCount = await _cacheService.RemoveByPatternAsync(pattern);
            return new SuccessDataResult<object>(new { RemovedCount = removedCount, Pattern = pattern }, $"Company cache'i başarıyla temizlendi. {removedCount} adet key silindi");
        }

        [SecuredOperation("owner")]
        public async Task<IDataResult<object>> ClearCacheByPatternAsync(string pattern, CancellationToken ct = default)
        {
            var companyId = _companyContext.GetCompanyId();
            if (companyId <= 0)
                return new ErrorDataResult<object>("Geçersiz company ID");

            if (!pattern.StartsWith($"gym:{companyId}:"))
            {
                pattern = $"gym:{companyId}:{pattern.TrimStart('*')}";
            }

            var removedCount = await _cacheService.RemoveByPatternAsync(pattern);
            return new SuccessDataResult<object>(new { RemovedCount = removedCount, Pattern = pattern }, $"Pattern cache'i başarıyla temizlendi. {removedCount} adet key silindi");
        }

        [SecuredOperation("owner")]
        public async Task<IDataResult<object>> GetTenantCacheDetailsAsync(CancellationToken ct = default)
        {
            var companyId = _companyContext.GetCompanyId();
            if (companyId <= 0)
                return new ErrorDataResult<object>("Geçersiz company ID");

            var details = await GetCompanyCacheDetails(companyId);
            return new SuccessDataResult<object>(details, "Company cache detayları başarıyla getirildi");
        }

        [SecuredOperation("owner")]
        public async Task<IDataResult<object>> WarmupCacheAsync(CacheWarmupRequestDto request, CancellationToken ct = default)
        {
            var companyId = _companyContext.GetCompanyId();
            if (companyId <= 0)
                return new ErrorDataResult<object>("Geçersiz company ID");

            var result = await PerformCacheWarmup(companyId, request);
            return new SuccessDataResult<object>(result, "Cache warmup işlemi başarıyla tamamlandı");
        }

        [SecuredOperation("owner")]
        public async Task<IDataResult<object>> GetRealtimeMetricsAsync(CancellationToken ct = default)
        {
            var companyId = _companyContext.GetCompanyId();
            if (companyId <= 0)
                return new ErrorDataResult<object>("Geçersiz company ID");

            var metrics = await GetRealtimeCacheMetrics(companyId);
            return new SuccessDataResult<object>(metrics, "Real-time cache metrics başarıyla getirildi");
        }

        [SecuredOperation("owner")]
        public async Task<IDataResult<object>> DeleteCacheKeyAsync(string key, CancellationToken ct = default)
        {
            var companyId = _companyContext.GetCompanyId();
            if (companyId <= 0)
                return new ErrorDataResult<object>("Geçersiz company ID");

            if (!key.StartsWith($"gym:{companyId}:"))
                return new ErrorDataResult<object>("Bu cache key'ine erişim yetkiniz yok");

            var removed = await _cacheService.RemoveAsync(key);
            return new SuccessDataResult<object>(new { Key = key, Removed = removed }, removed ? "Cache key başarıyla silindi" : "Cache key bulunamadı");
        }

        [SecuredOperation("owner")]
        public async Task<IDataResult<object>> GetCacheKeyValueAsync(string key, CancellationToken ct = default)
        {
            var companyId = _companyContext.GetCompanyId();
            if (companyId <= 0)
                return new ErrorDataResult<object>("Geçersiz company ID");

            if (!key.StartsWith($"gym:{companyId}:"))
                return new ErrorDataResult<object>("Bu cache key'ine erişim yetkiniz yok");

            var database = _connectionMultiplexer.GetDatabase();
            var value = await database.StringGetAsync(key);
            var ttl = await database.KeyTimeToLiveAsync(key);

            var result = new
            {
                Key = key,
                Value = value.HasValue ? value.ToString() : null,
                HasValue = value.HasValue,
                TTL = ttl?.TotalSeconds,
                Size = value.HasValue ? System.Text.Encoding.UTF8.GetByteCount(value) : 0
            };

            return new SuccessDataResult<object>(result, "Cache key değeri başarıyla getirildi");
        }

        [SecuredOperation("owner")]
        public async Task<IDataResult<object>> GetAllCompaniesStatisticsAsync(CancellationToken ct = default)
        {
            var companiesResult = await _companyService.GetAllAsync(ct);
            if (!companiesResult.Success)
                return new ErrorDataResult<object>("Şirketler alınamadı");

            var companies = companiesResult.Data;
            var allStats = new List<object>();

            foreach (var company in companies.Where(c => c.IsActive == true))
            {
                var stats = await GetCompanyCacheStatistics(company.CompanyID);
                allStats.Add(new
                {
                    CompanyId = company.CompanyID,
                    CompanyName = company.CompanyName,
                    Statistics = stats,
                    IsActive = company.IsActive,
                    CreationDate = company.CreationDate
                });
            }

            var totalStats = new
            {
                TotalCompanies = companies.Count(c => c.IsActive == true),
                TotalCacheKeys = (long)allStats.Sum(s => (int)((dynamic)s).Statistics.TotalKeys),
                TotalMemoryUsage = (long)allStats.Sum(s => (long)((dynamic)s).Statistics.TotalMemoryUsage),
                AverageKeysPerCompany = allStats.Any() ? allStats.Average(s => (int)((dynamic)s).Statistics.TotalKeys) : 0,
                Companies = allStats
            };

            return new SuccessDataResult<object>(totalStats, "Tüm şirket cache istatistikleri başarıyla getirildi");
        }

        [SecuredOperation("owner")]
        public async Task<IDataResult<object>> GetSpecificCompanyCacheDetailsAsync(int companyId, CancellationToken ct = default)
        {
            var companyResult = await _companyService.GetByIdAsync(companyId, ct);
            if (!companyResult.Success)
                return new ErrorDataResult<object>("Şirket bulunamadı");

            var details = await GetCompanyCacheDetails(companyId);
            var result = new
            {
                Company = companyResult.Data,
                CacheDetails = details
            };
            return new SuccessDataResult<object>(result, "Şirket cache detayları başarıyla getirildi");
        }

        [SecuredOperation("owner")]
        public async Task<IDataResult<object>> ClearSpecificCompanyCacheAsync(int companyId, CancellationToken ct = default)
        {
            var companyResult = await _companyService.GetByIdAsync(companyId, ct);
            if (!companyResult.Success)
                return new ErrorDataResult<object>("Şirket bulunamadı");

            var pattern = $"gym:{companyId}:*";
            var removedCount = await _cacheService.RemoveByPatternAsync(pattern);
            return new SuccessDataResult<object>(new { CompanyId = companyId, CompanyName = companyResult.Data.CompanyName, RemovedCount = removedCount, Pattern = pattern }, $"{companyResult.Data.CompanyName} şirketinin cache'i başarıyla temizlendi. {removedCount} adet key silindi");
        }

        [SecuredOperation("owner")]
        public async Task<IDataResult<object>> BulkClearCompaniesCacheAsync(BulkCacheOperationRequestDto request, CancellationToken ct = default)
        {
            var results = new List<object>();
            var totalRemovedCount = 0L;

            foreach (var companyId in request.CompanyIds)
            {
                var companyResult = await _companyService.GetByIdAsync(companyId, ct);
                if (!companyResult.Success)
                {
                    results.Add(new { CompanyId = companyId, Success = false, Message = "Şirket bulunamadı" });
                    continue;
                }

                try
                {
                    var pattern = $"gym:{companyId}:*";
                    var removedCount = await _cacheService.RemoveByPatternAsync(pattern);
                    totalRemovedCount += removedCount;

                    results.Add(new { CompanyId = companyId, CompanyName = companyResult.Data.CompanyName, Success = true, RemovedCount = removedCount, Message = $"{removedCount} adet key silindi" });
                }
                catch (Exception ex)
                {
                    results.Add(new { CompanyId = companyId, CompanyName = companyResult.Data.CompanyName, Success = false, Message = ex.Message });
                }
            }

            return new SuccessDataResult<object>(new { TotalRemovedCount = totalRemovedCount, ProcessedCompanies = request.CompanyIds.Count, Results = results }, $"Toplu cache temizleme tamamlandı. Toplam {totalRemovedCount} adet key silindi");
        }

        private async Task<object> GetCompanyCacheStatistics(int companyId)
        {
            var database = _connectionMultiplexer.GetDatabase();
            var server = _connectionMultiplexer.GetServer(_connectionMultiplexer.GetEndPoints().First());

            var pattern = $"gym:{companyId}:*";

            var totalKeys = 0;
            var totalMemoryUsage = 0L;
            var keysByEntity = new Dictionary<string, int>();

            await foreach (var key in server.KeysAsync(pattern: pattern))
            {
                try
                {
                    var keyStr = key.ToString();
                    var keyValue = await database.StringGetAsync(keyStr);
                    if (keyValue.HasValue)
                    {
                        var keySize = System.Text.Encoding.UTF8.GetByteCount(keyStr);
                        var valueSize = System.Text.Encoding.UTF8.GetByteCount(keyValue.ToString());
                        var overhead = 64; // approx
                        totalMemoryUsage += keySize + valueSize + overhead;
                    }

                    var keyParts = keyStr.Split(':');
                    if (keyParts.Length >= 3)
                    {
                        var entity = keyParts[2];
                        keysByEntity[entity] = keysByEntity.GetValueOrDefault(entity, 0) + 1;
                    }
                }
                catch
                {
                    // ignore per-key errors
                }
                finally
                {
                    totalKeys++;
                }
            }

            return new
            {
                CompanyId = companyId,
                TotalKeys = totalKeys,
                TotalMemoryUsage = totalMemoryUsage,
                TotalMemoryUsageMB = Math.Round(totalMemoryUsage / 1024.0 / 1024.0, 2),
                KeysByEntity = keysByEntity,
                LastUpdated = DateTime.UtcNow
            };
        }

        private async Task<object> GetRedisServerInfo()
        {
            try
            {
                var server = _connectionMultiplexer.GetServer(_connectionMultiplexer.GetEndPoints().First());
                var info = await server.InfoAsync();

                var serverInfo = info.FirstOrDefault(i => i.Key == "Server");
                var memoryInfo = info.FirstOrDefault(i => i.Key == "Memory");

                var version = serverInfo?.FirstOrDefault(s => s.Key == "redis_version");
                var uptime = serverInfo?.FirstOrDefault(s => s.Key == "uptime_in_seconds");
                var usedMem = memoryInfo?.FirstOrDefault(m => m.Key == "used_memory");
                var usedMemHuman = memoryInfo?.FirstOrDefault(m => m.Key == "used_memory_human");
                var maxMem = memoryInfo?.FirstOrDefault(m => m.Key == "maxmemory");
                var maxMemHuman = memoryInfo?.FirstOrDefault(m => m.Key == "maxmemory_human");

                return new
                {
                    Version = version?.Value.ToString() ?? "Unknown",
                    UptimeInSeconds = uptime?.Value.ToString() ?? "0",
                    UsedMemory = usedMem?.Value.ToString() ?? "0",
                    UsedMemoryHuman = usedMemHuman?.Value.ToString() ?? "0B",
                    MaxMemory = maxMem?.Value.ToString() ?? "0",
                    MaxMemoryHuman = maxMemHuman?.Value.ToString() ?? "0B"
                };
            }
            catch
            {
                return new { Error = "Server bilgileri alınamadı" };
            }
        }

        private async Task<object> GetKeysByPatternInternal(string pattern, int page, int size)
        {
            var server = _connectionMultiplexer.GetServer(_connectionMultiplexer.GetEndPoints().First());
            var database = _connectionMultiplexer.GetDatabase();

            var totalCount = 0;
            var keyBuffer = new List<RedisKey>();

            await foreach (var key in server.KeysAsync(pattern: pattern))
            {
                totalCount++;
                if (totalCount > (page - 1) * size && keyBuffer.Count < size)
                {
                    keyBuffer.Add(key);
                }
                if (keyBuffer.Count == size)
                    break;
            }

            var totalPages = (int)Math.Ceiling((double)totalCount / size);

            var keyDetails = new List<object>();
            foreach (var key in keyBuffer)
            {
                var keyStr = key.ToString();
                try
                {
                    var ttl = await database.KeyTimeToLiveAsync(keyStr);
                    var type = await database.KeyTypeAsync(keyStr);
                    var keyValue = await database.StringGetAsync(keyStr);

                    keyDetails.Add(new { Key = keyStr, Type = type.ToString(), TTL = ttl?.TotalSeconds, MemoryUsage = keyValue.HasValue ? keyValue.ToString().Length : 0, CreatedAt = DateTime.UtcNow.AddSeconds(-(ttl?.TotalSeconds ?? 0)) });
                }
                catch
                {
                    keyDetails.Add(new { Key = keyStr, Type = "Unknown", TTL = (double?)null, MemoryUsage = 0, CreatedAt = (DateTime?)null });
                }
            }

            return new
            {
                Keys = keyDetails,
                Pagination = new { CurrentPage = page, PageSize = size, TotalCount = totalCount, TotalPages = totalPages, HasNextPage = page < totalPages, HasPreviousPage = page > 1 },
                Pattern = pattern
            };
        }

        private async Task<object> GetCompanyCacheDetails(int companyId)
        {
            var statistics = await GetCompanyCacheStatistics(companyId);
            var healthResult = await GetCacheHealthAsync();

            return new
            {
                Statistics = statistics,
                Health = healthResult.Data,
                CompanyId = companyId,
                CachePatterns = new[]
                {
                    $"gym:{companyId}:member:*",
                    $"gym:{companyId}:payment:*",
                    $"gym:{companyId}:membership:*",
                    $"gym:{companyId}:user:*",
                    $"gym:{companyId}:company:*"
                }
            };
        }

        private async Task<object> PerformCacheWarmup(int companyId, CacheWarmupRequestDto request)
        {
            var warmupResults = new List<object>();
            var stopwatch = Stopwatch.StartNew();

            if (request.WarmupMembers)
            {
                warmupResults.Add(new { Entity = "Members", Status = "Completed", Duration = "0ms" });
            }

            if (request.WarmupPayments)
            {
                warmupResults.Add(new { Entity = "Payments", Status = "Completed", Duration = "0ms" });
            }

            if (request.WarmupMemberships)
            {
                warmupResults.Add(new { Entity = "Memberships", Status = "Completed", Duration = "0ms" });
            }

            stopwatch.Stop();

            return new { CompanyId = companyId, TotalDuration = stopwatch.ElapsedMilliseconds, Results = warmupResults, CompletedAt = DateTime.UtcNow };
        }

        private async Task<object> GetRealtimeCacheMetrics(int companyId)
        {
            var statistics = await GetCompanyCacheStatistics(companyId);
            var health = await GetRedisServerInfo();
            var database = _connectionMultiplexer.GetDatabase();

            var stopwatch = Stopwatch.StartNew();
            await database.PingAsync();
            stopwatch.Stop();

            return new
            {
                CompanyId = companyId,
                Timestamp = DateTime.UtcNow,
                Statistics = statistics,
                Performance = new { ResponseTime = stopwatch.ElapsedMilliseconds, IsConnected = _connectionMultiplexer.IsConnected, ConnectionCount = _connectionMultiplexer.GetCounters().Interactive.SocketCount },
                ServerInfo = health,
                TopCacheKeys = await GetTopCacheKeys(companyId, 10)
            };
        }

        private async Task<object[]> GetTopCacheKeys(int companyId, int count)
        {
            var pattern = $"gym:{companyId}:*";
            var server = _connectionMultiplexer.GetServer(_connectionMultiplexer.GetEndPoints().First());

            var selected = new List<RedisKey>(capacity: count);
            await foreach (var key in server.KeysAsync(pattern: pattern))
            {
                selected.Add(key);
                if (selected.Count >= count)
                    break;
            }

            var topKeys = new List<object>();
            foreach (var key in selected)
            {
                var keyStr = key.ToString();
                var ttl = await _connectionMultiplexer.GetDatabase().KeyTimeToLiveAsync(keyStr);
                var type = await _connectionMultiplexer.GetDatabase().KeyTypeAsync(keyStr);
                topKeys.Add(new { Key = keyStr, TTL = ttl?.TotalSeconds ?? -1, Type = type });
            }

            return topKeys.ToArray();
        }
    }
}

