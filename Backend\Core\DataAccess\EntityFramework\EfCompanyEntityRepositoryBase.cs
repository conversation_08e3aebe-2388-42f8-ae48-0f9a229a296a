using Core.Entities;
using Core.Utilities.Security.CompanyContext;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading;
using System.Threading.Tasks;

namespace Core.DataAccess.EntityFramework
{
    public class EfCompanyEntityRepositoryBase<TEntity, TContext> : EfEntityRepositoryBase<TEntity, TContext>, IEntityRepository<TEntity>
        where TEntity : class, ICompanyEntity, new()
        where TContext : DbContext
    {
        private readonly ICompanyContext _companyContext;

        // Constructor injection (Scalability için - sadece DI pattern)
        public EfCompanyEntityRepositoryBase(ICompanyContext companyContext, TContext context) : base(context)
        {
            _companyContext = companyContext ?? throw new ArgumentNullException(nameof(companyContext));
        }


        public new async Task<List<TEntity>> GetAllAsync(Expression<Func<TEntity, bool>> filter = null, CancellationToken ct = default)
        {
            int companyId = _companyContext.GetCompanyId();
            if (companyId <= 0)
            {
                return new List<TEntity>();
            }

            IQueryable<TEntity> query = _context.Set<TEntity>().AsNoTracking().Where(e => e.CompanyID == companyId);
            if (filter != null)
            {
                query = query.Where(filter);
            }

            return await query.ToListAsync(ct);
        }


        public new async Task<TEntity> GetAsync(Expression<Func<TEntity, bool>> filter, CancellationToken ct = default)
        {
            int companyId = _companyContext.GetCompanyId();
            if (companyId <= 0)
            {
                return null;
            }

            return await _context.Set<TEntity>()
                .AsNoTracking()
                .Where(e => e.CompanyID == companyId)
                .SingleOrDefaultAsync(filter, ct);
        }


        public new async Task AddAsync(TEntity entity, CancellationToken ct = default)
        {
            int companyId = _companyContext.GetCompanyId();
            if (companyId <= 0)
            {
                throw new Exception("Geçerli bir şirket ID'si bulunamadı.");
            }

            entity.CompanyID = companyId;
            await base.AddAsync(entity, ct);
        }


        public new async Task UpdateAsync(TEntity entity, CancellationToken ct = default)
        {
            int companyId = _companyContext.GetCompanyId();
            if (companyId <= 0)
            {
                throw new Exception("Geçerli bir şirket ID'si bulunamadı.");
            }

            entity.CompanyID = companyId;
            await base.UpdateAsync(entity, ct);
        }
    }
}
