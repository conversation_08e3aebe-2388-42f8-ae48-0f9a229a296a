using Castle.DynamicProxy;
using Core.CrossCuttingConcerns.Caching;
using Core.Utilities.Interceptors;
using Core.Utilities.IoC;
using Core.Utilities.Security.CompanyContext;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Linq;
using System.Threading.Tasks;
using Core.CrossCuttingConcerns.Logging;

namespace Core.Aspects.Autofac.Caching
{
    /// <summary>
    /// Async-aware cache remove aspect. CUD işlemlerinden sonra, method başarıyla tamamlanırsa
    /// pattern bazlı cache invalidation yapar. Multi-tenant (CompanyID) farkındalığı vardır.
    /// </summary>
    public class CacheRemoveAspectAsync : MethodInterceptionBaseAttribute
    {
        private readonly string[] _patterns;
        private readonly ICacheService _cacheService;
        private readonly ICompanyContext _companyContext;
        private readonly ILogService _logService;

        public CacheRemoveAspectAsync(params string[] patterns)
        {
            _patterns = patterns ?? Array.Empty<string>();
            _cacheService = ServiceTool.ServiceProvider.GetService<ICacheService>();
            _companyContext = ServiceTool.ServiceProvider.GetService<ICompanyContext>();
            _logService = ServiceTool.ServiceProvider.GetService<ILogService>();
        }

        public override void Intercept(IInvocation invocation)
        {
            // Orijinal methodu çağır
            invocation.Proceed();

            var returnType = invocation.Method.ReturnType;

            // Task (void)
            if (returnType == typeof(Task))
            {
                var task = (Task)invocation.ReturnValue;
                invocation.ReturnValue = AfterAsync(task);
                return;
            }

            // Task<T>
            if (returnType.IsGenericType && returnType.GetGenericTypeDefinition() == typeof(Task<>))
            {
                var task = invocation.ReturnValue; // Task<T>
                var method = typeof(CacheRemoveAspectAsync).GetMethod(nameof(AfterAsyncWithResult), System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                var genericMethod = method.MakeGenericMethod(returnType.GetGenericArguments()[0]);
                invocation.ReturnValue = genericMethod.Invoke(this, new object[] { task });
                return;
            }

            // Sync akış (nadiren kullanılmalı): IResult.Success varsa kontrol et, yoksa varsayılan temizle
            InvalidateIfSuccessful(invocation.ReturnValue);
        }

        private async Task AfterAsync(Task task)
        {
            try
            {
                await task.ConfigureAwait(false);
                // Task (void) için IResult bilgisi yok: başarılı kabul edip temizle
                await InvalidateAsync().ConfigureAwait(false);
            }
            catch
            {
                // başarısızlıkta invalidation yapma
                throw;
            }
        }

        private async Task<T> AfterAsyncWithResult<T>(Task<T> task)
        {
            try
            {
                var result = await task.ConfigureAwait(false);
                InvalidateIfSuccessful(result);
                return result;
            }
            catch
            {
                // başarısızlıkta invalidation yapma
                throw;
            }
        }

        private void InvalidateIfSuccessful(object resultOrNull)
        {
            bool success = true;

            if (resultOrNull != null)
            {
                var type = resultOrNull.GetType();
                var successProp = type.GetProperty("Success");
                if (successProp != null)
                {
                    success = (bool)successProp.GetValue(resultOrNull);
                }
            }

            if (success)
            {
                // Fire-and-forget pattern: async cache invalidation'ı bloklamadan çalıştır
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await InvalidateAsync().ConfigureAwait(false);
                    }
                    catch (Exception ex)
                    {
                        // Cache invalidation hatası iş akışını bozmamalı, sadece logla
                        _logService?.Error($"CacheRemoveAspectAsync fire-and-forget error: {ex.Message}");
                    }
                });
            }
        }

        private async Task InvalidateAsync()
        {
            var companyId = _companyContext.GetCompanyId();
            if (companyId <= 0 || _patterns.Length == 0) return;

            foreach (var pattern in _patterns)
            {
                try
                {
                    var resolvedPattern = pattern.Replace("{companyId}", companyId.ToString());
                    await _cacheService.RemoveByPatternAsync(resolvedPattern).ConfigureAwait(false);
                }
                catch (Exception ex)
                {
                    _logService?.Error($"CacheRemoveAspectAsync error: {ex.Message}");
                }
            }
        }
    }
}

