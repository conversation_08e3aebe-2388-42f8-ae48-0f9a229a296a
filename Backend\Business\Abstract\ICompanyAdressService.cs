﻿using Core.Utilities.Results;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Business.Abstract
{
    public interface ICompanyAdressService
    {
        Task<IDataResult<List<CompanyAdress>>> GetAllAsync(CancellationToken ct = default);
        Task<IResult> AddAsync(CompanyAdress companyAdress, CancellationToken ct = default);
        Task<IResult> UpdateAsync(CompanyAdress companyAdress, CancellationToken ct = default);
        Task<IResult> DeleteAsync(int id, CancellationToken ct = default);
        Task<IDataResult<List<CompanyAdressDetailDto>>> GetCompanyAdressDetailsAsync(CancellationToken ct = default);

    }
}
