﻿using Core.Entities.Concrete;
using Core.Utilities.Results;
using Core.Utilities.Security.JWT;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
namespace Business.Abstract
{
    public interface IAuthService
    {
        Task<IDataResult<User>> RegisterAsync(UserForRegisterDto userForRegisterDto, string password, CancellationToken ct = default);
        Task<IDataResult<User>> RegisterMemberAsync(MemberForRegisterDto memberForRegisterDto, string password, CancellationToken ct = default);
        Task<IDataResult<User>> LoginAsync(UserForLoginDto userForLoginDto, string deviceInfo, CancellationToken ct = default);
        Task<IResult> UserExistsAsync(string email, CancellationToken ct = default);
        Task<IDataResult<AccessToken>> CreateAccessTokenAsync(User user, string deviceInfo, CancellationToken ct = default);
        Task<IDataResult<AccessToken>> CreateAccessTokenWithRefreshTokenAsync(string refreshToken, string ipAddress, string deviceInfo, CancellationToken ct = default);
        Task<IDataResult<AccessToken>> ChangeCompanyAsync(int userId, int companyId, string deviceInfo, CancellationToken ct = default);
        Task<IResult> ChangePasswordAsync(int userId, string currentPassword, string newPassword, CancellationToken ct = default);
        Task<IDataResult<bool>> CheckPasswordChangeRequiredAsync(int userId, CancellationToken ct = default);

        // Rate Limiting Methods (ASYNC only - KEEP_SYNC=false)
        Task<string> GenerateDeviceFingerprintAsync(string ipAddress, string userAgent, string deviceInfo, CancellationToken ct = default);
        Task<IDataResult<int>> GetRemainingLoginBanTimeAsync(string ipAddress, string deviceFingerprint, CancellationToken ct = default);
        Task<IDataResult<int>> GetRemainingRegisterBanTimeAsync(string ipAddress, CancellationToken ct = default);

        // Device/Session Management (ASYNC)
        Task<IResult> RevokeRefreshTokenAsync(string refreshToken, CancellationToken ct = default);
        Task<IResult> RevokeAllDevicesAsync(int userId, CancellationToken ct = default);
        Task<IResult> RevokeDeviceAsync(int deviceId, CancellationToken ct = default);
        Task<IDataResult<List<UserDeviceDto>>> GetUserDevicesAsync(int userId, CancellationToken ct = default);
    }
}
