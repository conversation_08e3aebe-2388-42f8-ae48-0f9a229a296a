using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Business.ValidationRules.FluentValidation;


using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Transaction;
using Core.Aspects.Autofac.Validation;
using Core.Utilities.Paging;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using MemberFilter = Entities.DTOs.MemberFilter;
using System.Threading;
using System.Threading.Tasks;

namespace Business.Concrete
{
    public class MemberManager : IMemberService
    {
        IMemberDal _memberDal;
        IMembershipDal _membershipDal;
        IEntryExitHistoryService _entryExitHistoryService;
        private readonly Core.Utilities.Security.CompanyContext.ICompanyContext _companyContext;
        private readonly IQrCodeEncryptionService _qrCodeEncryptionService;

        public MemberManager(
            IMemberDal memberDal,
            IMembershipDal membershipDal,
            IEntryExitHistoryService entryExitHistoryService,
            Core.Utilities.Security.CompanyContext.ICompanyContext companyContext,
            IQrCodeEncryptionService qrCodeEncryptionService)
        {
            _memberDal = memberDal;
            _membershipDal = membershipDal;
            _entryExitHistoryService = entryExitHistoryService;
            _companyContext = companyContext;
            _qrCodeEncryptionService = qrCodeEncryptionService;
        }



        // Async varyant — Prompt 2
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(600)]
        public async Task<IDataResult<Dictionary<string, int>>> GetBranchCountsAsync(CancellationToken ct = default)
        {
            var companyId = _companyContext.GetCompanyId();
            return await _memberDal.GetBranchCountsAsync(companyId, ct);
        }



        // Async (CT'li) yüzeyler — Sync yüzeyler korunur
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(600)]
        public async Task<IDataResult<PaginatedResult<Member>>> GetAllPaginatedAsync(MemberPagingParameters parameters, CancellationToken ct = default)
        {
            var result = await _memberDal.GetAllPaginatedAsync(parameters, ct);
            return new SuccessDataResult<PaginatedResult<Member>>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(1800)]
        public async Task<IDataResult<PaginatedResult<MemberFilter>>> GetMemberDetailsPaginatedAsync(MemberPagingParameters parameters, CancellationToken ct = default)
        {
            var result = await _memberDal.GetMemberDetailsPaginatedAsync(parameters, ct);
            return new SuccessDataResult<PaginatedResult<MemberFilter>>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(1800)]
        public async Task<IDataResult<PaginatedResult<MemberFilter>>> GetMembersByMultiplePackagesAsync(MemberPagingParameters parameters, CancellationToken ct = default)
        {
            var result = await _memberDal.GetMembersByMultiplePackagesAsync(parameters, ct);
            return new SuccessDataResult<PaginatedResult<MemberFilter>>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(1800)]
        public async Task<IDataResult<PaginatedResult<Member>>> GetMembersWithBalancePaginatedAsync(MemberPagingParameters parameters, string balanceFilter, CancellationToken ct = default)
        {
            var result = await _memberDal.GetMembersWithBalancePaginatedAsync(parameters, balanceFilter, ct);
            return new SuccessDataResult<PaginatedResult<Member>>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(1800)]
        public async Task<IDataResult<List<GetActiveMemberDto>>> GetActiveMembersAsync(CancellationToken ct = default)
        {
            var result = await _memberDal.GetActiveMembersAsync(ct);
            return new SuccessDataResult<List<GetActiveMemberDto>>(result);
        }





        // Async varyant — Prompt 2
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(600)]
        public async Task<IDataResult<int>> GetTotalActiveMembersAsync(CancellationToken ct = default)
        {
            var companyId = _companyContext.GetCompanyId();
            return await _memberDal.GetTotalActiveMembersAsync(companyId, ct);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(600)]
        public async Task<IDataResult<int>> GetTotalRegisteredMembersAsync(CancellationToken ct = default)
        {
            var companyId = _companyContext.GetCompanyId();
            return await _memberDal.GetTotalRegisteredMembersAsync(companyId, ct);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(600)]
        public async Task<IDataResult<Dictionary<string, int>>> GetActiveMemberCountsAsync(CancellationToken ct = default)
        {
            var companyId = _companyContext.GetCompanyId();
            return await _memberDal.GetActiveMemberCountsAsync(companyId, ct);
        }




        // Async varyantlar - TodayEntries
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(300)]
        public async Task<IDataResult<List<MemberEntryDto>>> GetTodayEntriesAsync(DateTime date, CancellationToken ct = default)
        {
            var result = await _memberDal.GetTodayEntriesAsync(date, ct);
            return new SuccessDataResult<List<MemberEntryDto>>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(300)]
        public async Task<IDataResult<PaginatedResult<MemberEntryDto>>> GetTodayEntriesPaginatedAsync(MemberEntryPagingParameters parameters, CancellationToken ct = default)
        {
            var result = await _memberDal.GetTodayEntriesPaginatedAsync(parameters, ct);
            return new SuccessDataResult<PaginatedResult<MemberEntryDto>>(result);
        }


        // Sync Add kaldırıldı (async sürüm kullanılıyor)
        [SecuredOperation("owner,admin")]
        [ValidationAspect(typeof(MemberValidator))]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("Member")]
        public async Task<IResult> AddAsync(Member member, CancellationToken ct = default)
        {
            member.Name = member.Name.ToUpper();
            var companyId = _companyContext.GetCompanyId();
            return await _memberDal.AddMemberWithUserManagementAsync(member, companyId, ct);
        }


        // Sync AddWithCard kaldırıldı (async sürüm kullanılıyor)

        // Async varyant
        [SecuredOperation("owner,admin")]
        [ValidationAspect(typeof(MemberValidator))]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("Member")]
        public async Task<IResult> AddWithCardAsync(Member member, CancellationToken ct = default)
        {
            member.Name = member.Name.ToUpper();
            var companyId = _companyContext.GetCompanyId();
            return await _memberDal.AddMemberWithCardAsync(member, companyId, ct);
        }

        // Sync Delete kaldırıldı (async sürüm kullanılıyor)

        // Async varyant — Delete
        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("Member")]
        public async Task<IResult> DeleteAsync(int id, CancellationToken ct = default)
        {
            var companyId = _companyContext.GetCompanyId();
            return await _memberDal.DeleteMemberWithUserManagementAsync(id, companyId, ct);
        }

        // Async varyant — Doğum günleri
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(3600)]
        public async Task<IDataResult<List<MemberBirthdayDto>>> GetUpcomingBirthdaysAsync(int days, CancellationToken ct = default)
        {
            var result = await _memberDal.GetUpcomingBirthdaysAsync(days, ct);
            return new SuccessDataResult<List<MemberBirthdayDto>>(result);
        }


        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(1800)] // 30 dakika cache - Tüm üyeler listesi (async)
        public async Task<IDataResult<List<Member>>> GetAllAsync(CancellationToken ct = default)
        {
            var list = await _memberDal.GetAllAsync(ct);
            // deterministik sıralama (ek güvence): MemberID asc
            list = list.OrderBy(m => m.MemberID).ToList();
            return new SuccessDataResult<List<Member>>(list);
        }


        // Prompt 4 — Async implementasyonlar
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(1800)]
        public async Task<IDataResult<List<MembeFilterDto>>> GetMemberDetailsAsync(CancellationToken ct = default)
        {
            var result = await _memberDal.GetMemberDetailsAsync(ct);
            return new SuccessDataResult<List<MembeFilterDto>>(result, "Üye detayları listelendi (async)");
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(1800)]
        public async Task<IDataResult<List<MemberEntryExitHistoryDto>>> GetMemberEntryExitHistoryAsync(CancellationToken ct = default)
        {
            var result = await _memberDal.GetMemberEntryExitHistoryAsync(ct);
            return new SuccessDataResult<List<MemberEntryExitHistoryDto>>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(1800)]
        public async Task<IDataResult<List<MemberRemainingDayDto>>> GetMemberRemainingDayAsync(CancellationToken ct = default)
        {
            var result = await _memberDal.GetMemberRemainingDayAsync(ct);
            return new SuccessDataResult<List<MemberRemainingDayDto>>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(300)]
        public async Task<IDataResult<List<MemberEntryDto>>> GetMemberEntriesBySearchAsync(string searchText, CancellationToken ct = default)
        {
            var result = await _memberDal.GetMemberEntriesBySearchAsync(searchText, ct);
            return new SuccessDataResult<List<MemberEntryDto>>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(300)]
        public async Task<IDataResult<PaginatedResult<MemberEntryDto>>> GetMemberEntriesBySearchPaginatedAsync(MemberEntryPagingParameters parameters, CancellationToken ct = default)
        {
            var result = await _memberDal.GetMemberEntriesBySearchPaginatedAsync(parameters, ct);
            return new SuccessDataResult<PaginatedResult<MemberEntryDto>>(result);
        }


        // Prompt 3 — Async implementasyonlar
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(1800)]
        public async Task<IDataResult<MemberDetailWithHistoryDto>> GetMemberDetailByIdAsync(int memberId, CancellationToken ct = default)
        {
            var companyId = _companyContext.GetCompanyId();
            // DAL sync hesaplama metodu var; burada Task.FromResult ile sarmalamak yerine DAL’a async muadili eklemek daha iyi olurdu.
            // Şimdilik mevcut hesaplamayı kullanıp Task.FromResult ile döndürelim.
            var r = await _memberDal.GetMemberDetailByIdWithCalculationsAsync(memberId, companyId, ct);
            return r;
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(300)]
        public async Task<IDataResult<GetMemberQRByPhoneNumberDto>> GetMemberQRByPhoneNumberAsync(string phoneNumber, CancellationToken ct = default)
        {
            var result = await _memberDal.GetMemberQRByPhoneNumberAsync(phoneNumber, _companyContext.GetCompanyId(), ct);
            if (result.Success && result.Data != null)
            {
                string encryptedQRCode = _qrCodeEncryptionService.CreateEncryptedQrToken(
                    result.Data.Name.GetHashCode(),
                    result.Data.ScanNumber
                );
                result.Data.ScanNumber = "MBR" + encryptedQRCode;
            }
            return result;
        }

        [SecuredOperation("member")]
        [PerformanceAspect(3)]
        [CacheAspect(300)]
        public async Task<IDataResult<GetMemberQRByPhoneNumberDto>> GetMemberQRByUserIdWithoutCompanyFilterAsync(int userId, CancellationToken ct = default)
        {
            var result = await _memberDal.GetMemberQRByUserIdWithoutCompanyFilterAsync(userId, ct);
            if (result.Success && result.Data != null)
            {
                string encryptedQRCode = _qrCodeEncryptionService.CreateEncryptedQrToken(
                    result.Data.Name.GetHashCode(),
                    result.Data.ScanNumber
                );
                result.Data.ScanNumber = "MBR" + encryptedQRCode;
            }
            return result;
        }

        [SecuredOperation("member")]
        [PerformanceAspect(3)]
        [CacheAspect(1800)]
        public async Task<IDataResult<MemberProfileDto>> GetMemberProfileByUserIdAsync(int userId, CancellationToken ct = default)
        {
            var r = await _memberDal.GetMemberProfileByUserIdAsync(userId, _companyContext.GetCompanyId(), ct);
            return r;
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(1800)]
        public async Task<IDataResult<List<Member>>> GetByMemberIdAsync(int memberid, CancellationToken ct = default)
        {
            var list = await _memberDal.GetAllAsync(c => c.MemberID == memberid, ct);
            return new SuccessDataResult<List<Member>>(list);
        }




        // Sync Update kaldırıldı (async sürüm kullanılıyor)

        // Async varyant
        [SecuredOperation("owner,admin")]
        [ValidationAspect(typeof(MemberValidator))]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("Member")]
        public async Task<IResult> UpdateAsync(Member member, CancellationToken ct = default)
        {
            var companyId = _companyContext.GetCompanyId();
            return await _memberDal.UpdateMemberWithUserManagementAsync(member, companyId, ct);
        }

        // Async varyant - ScanNumber
        [PerformanceAspect(2)]
        public async Task<IDataResult<MemberDetailDto>> GetMemberRemainingDaysForScanNumberAsync(string scanNumber, CancellationToken ct = default)
        {
            if (scanNumber.StartsWith("MBR"))
            {
                string encryptedQRCode = scanNumber.Substring(3);
                var decryptResult = _qrCodeEncryptionService.DecryptQrToken(encryptedQRCode);
                if (!decryptResult.Success || decryptResult.Data == null)
                {
                    return new ErrorDataResult<MemberDetailDto>(null, decryptResult.Message ?? "Geçersiz QR kod.");
                }
                var decryptedData = decryptResult.Data;
                if (!decryptedData.IsValid)
                {
                    return new ErrorDataResult<MemberDetailDto>(null, decryptedData.ErrorMessage ?? "QR kodun süresi dolmuş.");
                }
                return await _memberDal.GetMemberRemainingDaysForScanNumberAsync(decryptedData.ScanNumber, _companyContext.GetCompanyId(), ct);
            }
            else
            {
                return await _memberDal.GetMemberRemainingDaysForScanNumberAsync(scanNumber, _companyContext.GetCompanyId(), ct);
            }
        }


        // SOLID prensiplerine uygun refactoring sonrası artık kullanılmayan helper metotlar kaldırıldı
        // GenerateUniqueQRCode, CreateNewUserForMember ve GenerateRandomPart metotları
        // DAL katmanına taşındı



        [SecuredOperation("owner,admin")]
        public async Task<IDataResult<Member>> GetMemberByUserIdAsync(int userId, CancellationToken ct = default)
        {
            var member = await _memberDal.GetAsync(m => m.UserID == userId && m.IsActive == true, ct);
            if (member == null)
            {
                return new ErrorDataResult<Member>("Üye bulunamadı veya erişim yetkiniz yok.");
            }
            return new SuccessDataResult<Member>(member);
        }




        /// <summary>
        /// Kullanıcının profil bilgilerini günceller (sadece member rolü)
        /// User tablosunda: FirstName, LastName (tek kayıt)
        /// Member tablosunda: Adress, BirthDate (sadece mevcut şirketteki kayıt güncellenir)
        /// Not: Member.Name alanı güncellenmez (salon yöneticisi kontrolünde)
        /// </summary>
        [SecuredOperation("member")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("member", true)] // Role bazlı cache invalidation
        public async Task<IResult> UpdateMemberProfileAsync(int userId, MemberProfileUpdateDto profileUpdateDto, CancellationToken ct = default)
        {
            var companyId = _companyContext.GetCompanyId();
            return await _memberDal.UpdateMemberProfileAsync(userId, companyId, profileUpdateDto, ct);
        }
    }
}