﻿using Core.Utilities.Results;
using Entities.Concrete;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Business.Abstract
{
    public interface IEntryExitHistoryService
    {
        // Async yüzeyler (KEEP_SYNC=false)
        Task<IDataResult<List<EntryExitHistory>>> GetAllAsync(CancellationToken ct = default);
        Task<IResult> AddAsync(EntryExitHistory entryExitHistory, CancellationToken ct = default);
        Task<IResult> UpdateAsync(EntryExitHistory entryExitHistory, CancellationToken ct = default);
        Task<IResult> DeleteAsync(int id, CancellationToken ct = default);

        // QR kod taraması için özel async metotlar - CompanyID parametreli
        Task<IResult> AddWithCompanyIdAsync(EntryExitHistory entryExitHistory, int companyId, CancellationToken ct = default);
        Task<IResult> UpdateWithCompanyIdAsync(EntryExitHistory entryExitHistory, int companyId, CancellationToken ct = default);
    }
}
