using Entities.Concrete;
using FluentValidation;

namespace Business.ValidationRules.FluentValidation
{
    public class MembershipFreezeHistoryValidator : AbstractValidator<MembershipFreezeHistory>
    {
        public MembershipFreezeHistoryValidator()
        {
            RuleFor(x => x.MembershipID)
                .GreaterThan(0).WithMessage("Geçerli bir üyelik seçimi gereklidir.");

            RuleFor(x => x.StartDate)
                .NotEmpty().WithMessage("Başlangıç tarihi boş bırakılamaz.");

            RuleFor(x => x.PlannedEndDate)
                .NotEmpty().WithMessage("Planlanan bitiş tarihi boş bırakılamaz.")
                .GreaterThan(x => x.StartDate).WithMessage("Bitiş tarihi başlangıç tarihinden sonra olmalıdır.");

            RuleFor(x => x.FreezeDays)
                .GreaterThan(0).WithMessage("Dondurma gün sayısı 0'dan b<PERSON><PERSON><PERSON><PERSON> olmalıdır.");

            RuleFor(x => x.UsedDays)
                .GreaterThanOrEqualTo(0).When(x => x.UsedDays.HasValue).WithMessage("Kullanılan gün sayısı negatif olamaz.")
                .LessThanOrEqualTo(x => x.FreezeDays).When(x => x.UsedDays.HasValue).WithMessage("Kullanılan gün sayısı dondurma gün sayısını aşamaz.");

            RuleFor(x => x.CancellationType)
                .MaximumLength(100).When(x => x.CancellationType != null).WithMessage("İptal türü en fazla 100 karakter olabilir.");
        }
    }
}

