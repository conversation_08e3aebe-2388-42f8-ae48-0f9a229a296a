using Core.Utilities.Results;
using System.Threading;
using System.Threading.Tasks;

namespace Business.Abstract
{
    public interface IAdvancedRateLimitService
    {
        // Login Rate Limiting
        Task<IResult> CheckLoginAttemptAsync(string ipAddress, string deviceFingerprint, CancellationToken ct = default);
        Task<IResult> RecordFailedLoginAsync(string ipAddress, string deviceFingerprint, CancellationToken ct = default);
        Task<IResult> RecordSuccessfulLoginAsync(string ipAddress, string deviceFingerprint, CancellationToken ct = default);
        Task<IDataResult<int>> GetRemainingLoginBanTimeAsync(string ipAddress, string deviceFingerprint, CancellationToken ct = default);

        // Register Rate Limiting
        Task<IResult> CheckRegisterAttemptAsync(string ipAddress, CancellationToken ct = default);
        Task<IResult> RecordSuccessfulRegisterAsync(string ipAddress, CancellationToken ct = default);
        Task<IDataResult<int>> GetRemainingRegisterBanTimeAsync(string ipAddress, CancellationToken ct = default);

        // Profile Image Upload Rate Limiting
        Task<IResult> CheckProfileImageUploadAttemptAsync(int userId, CancellationToken ct = default);
        Task<IResult> RecordProfileImageUploadAsync(int userId, CancellationToken ct = default);
        Task<IDataResult<int>> GetRemainingProfileImageUploadsAsync(int userId, CancellationToken ct = default);

        // File Download Rate Limiting
        Task<IResult> CheckFileDownloadAttemptAsync(int userId, CancellationToken ct = default);
        Task<IResult> RecordFileDownloadAsync(int userId, CancellationToken ct = default);
        Task<IDataResult<int>> GetRemainingFileDownloadsAsync(int userId, CancellationToken ct = default);

        // Device Fingerprinting
        Task<string> GenerateDeviceFingerprintAsync(string ipAddress, string userAgent, string deviceInfo, CancellationToken ct = default);
    }
}
