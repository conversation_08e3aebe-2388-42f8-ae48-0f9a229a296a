using Core.Utilities.Results;
using Microsoft.AspNetCore.Http;
using System.Threading;
using System.Threading.Tasks;

namespace Business.Abstract
{
    public interface IFileService
    {
        // Async metodlar (KEEP_SYNC=false)
        Task<IDataResult<string>> UploadProfileImageAsync(IFormFile file, int userId, CancellationToken ct = default);
        Task<IResult> DeleteProfileImageAsync(int userId, CancellationToken ct = default);
        Task<IDataResult<string>> GetProfileImagePathAsync(int userId, CancellationToken ct = default);

        // I<PERSON> yapmayan validasyon sync kalır
        IResult ValidateImageFile(IFormFile file);
    }
}
