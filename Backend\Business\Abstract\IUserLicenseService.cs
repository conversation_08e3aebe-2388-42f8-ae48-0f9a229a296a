﻿using Core.Utilities.Results;
using Entities.Concrete;
using Entities.DTOs;
using System.Threading;
using System.Threading.Tasks;

namespace Business.Abstract
{
    public interface IUserLicenseService
    {

        // ASYNC imzalar (sync korunur)
        Task<IDataResult<List<UserLicenseDto>>> GetAllAsync(CancellationToken ct = default);
        Task<IDataResult<PaginatedUserLicenseDto>> GetAllPaginatedAsync(int page, int pageSize, string searchTerm, string sortBy, string companyName, int? remainingDaysMin, int? remainingDaysMax, CancellationToken ct = default);
        Task<IDataResult<PaginatedUserLicenseDto>> GetExpiredAndPassiveAsync(int page, int pageSize, string searchTerm, CancellationToken ct = default);
        Task<IDataResult<UserLicenseDto>> GetByIdAsync(int id, CancellationToken ct = default);
        Task<IDataResult<List<UserLicenseDto>>> GetActiveByUserIdAsync(int userId, CancellationToken ct = default);
        Task<IDataResult<List<UserLicenseDto>>> GetMyActiveLicensesAsync(int userId, CancellationToken ct = default);
        Task<IResult> AddAsync(UserLicense userLicense, CancellationToken ct = default);
        Task<IResult> UpdateAsync(UserLicense userLicense, CancellationToken ct = default);
        Task<IResult> DeleteAsync(int id, CancellationToken ct = default);
        Task<IResult> PurchaseAsync(LicensePurchaseDto licensePurchaseDto, CancellationToken ct = default);
        Task<IResult> ExtendLicenseByPackageAsync(LicenseExtensionByPackageDto licenseExtensionByPackageDto, CancellationToken ct = default);
        Task<IResult> RevokeLicenseAsync(int userLicenseId, CancellationToken ct = default);
        Task<IDataResult<List<string>>> GetUserRolesAsync(int userId, CancellationToken ct = default);
    }

}
