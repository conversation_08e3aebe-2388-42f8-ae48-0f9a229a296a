﻿using Core.Utilities.Results;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Threading;


namespace Business.Abstract
{
    public interface IUserCompanyService
    {


        // Async imzalar (CT zorunlu zincir)
        Task<IDataResult<List<UserCompany>>> GetAllAsync(CancellationToken ct = default);
        Task<IResult> AddAsync(UserCompany userCompany, CancellationToken ct = default);
        Task<IResult> UpdateAsync(UserCompany userCompany, CancellationToken ct = default);
        Task<IResult> DeleteAsync(int id, CancellationToken ct = default);
        Task<IDataResult<List<UserCompanyDetailDto>>> GetUserCompanyDetailsAsync(CancellationToken ct = default);
        Task<IDataResult<int>> GetUserCompanyIdAsync(int userId, CancellationToken ct = default);
        Task<IDataResult<List<UserCompany>>> GetUserCompaniesAsync(int userId, CancellationToken ct = default);
        Task<IResult> UpdateActiveCompanyAsync(int userId, int companyId, CancellationToken ct = default);


    }
}
