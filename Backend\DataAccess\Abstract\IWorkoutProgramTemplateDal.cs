using Core.DataAccess;
using Core.Utilities.Results;
using Entities.Concrete;
using Entities.DTOs;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace DataAccess.Abstract
{
    public interface IWorkoutProgramTemplateDal : IEntityRepository<WorkoutProgramTemplate>
    {
        // Async varyantlar
        Task<List<WorkoutProgramTemplateListDto>> GetWorkoutProgramTemplateListAsync(CancellationToken ct = default);
        Task<WorkoutProgramTemplateDto> GetWorkoutProgramTemplateDetailAsync(int templateId, CancellationToken ct = default);
        Task<bool> CheckProgramNameExistsAsync(string programName, int? excludeId = null, CancellationToken ct = default);
        Task AddWorkoutProgramWithDaysAndExercisesAsync(WorkoutProgramTemplateAddDto dto, int companyId, CancellationToken ct = default);
        Task UpdateWorkoutProgramWithDaysAndExercisesAsync(WorkoutProgramTemplateUpdateDto dto, int companyId, CancellationToken ct = default);

        // SOLID prensiplerine uygun: Validation logic DAL katmanında
        Task<IDataResult<WorkoutProgramTemplate>> GetWorkoutProgramTemplateByIdWithValidationAsync(int templateId, CancellationToken ct = default);

        // SOLID prensiplerine uygun: Business rules validation ve operations DAL katmanında
        Task<IResult> AddWorkoutProgramWithBusinessRulesAsync(WorkoutProgramTemplateAddDto templateAddDto, int companyId, CancellationToken ct = default);
        Task<IResult> UpdateWorkoutProgramWithBusinessRulesAsync(WorkoutProgramTemplateUpdateDto templateUpdateDto, int companyId, CancellationToken ct = default);
        Task<IResult> SoftDeleteWorkoutProgramWithValidationAsync(int templateId, int companyId, CancellationToken ct = default);
    }
}
