using Core.Utilities.Results;
using Microsoft.AspNetCore.Http;
using System.Threading;
using System.Threading.Tasks;

namespace Business.Abstract
{
    public interface IProfileService
    {
        Task<IDataResult<string>> UploadProfileImageAsync(IFormFile file, int userId, CancellationToken ct = default);
        Task<IResult> DeleteProfileImageAsync(int userId, CancellationToken ct = default);
        Task<IResult> UpdateProfileImagePathAsync(int userId, string imagePath, CancellationToken ct = default);
    }
}
