﻿using Core.Entities;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading;
using System.Threading.Tasks;

namespace Core.DataAccess.EntityFramework
{
    public class EfEntityRepositoryBase<TEntity, TContext> : IEntityRepository<TEntity>
        where TEntity : class, IEntity, new()
        where TContext : DbContext
    {
        protected readonly TContext _context;

        // Constructor injection (Scalability için - sadece DI pattern)
        public EfEntityRepositoryBase(TContext context)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
        }


        public async Task AddAsync(TEntity entity, CancellationToken ct = default)
        {
            var addedEntity = _context.Entry(entity);
            addedEntity.State = EntityState.Added;

            if (addedEntity.Properties.Any(p => p.Metadata.Name == "CreationDate"))
            {
                addedEntity.Property("CreationDate").CurrentValue = DateTime.Now;
            }
            if (addedEntity.Properties.Any(p => p.Metadata.Name == "DeletedDate"))
            {
                addedEntity.Property("DeletedDate").IsModified = false;
            }
            if (addedEntity.Properties.Any(p => p.Metadata.Name == "UpdatedDate"))
            {
                addedEntity.Property("UpdatedDate").IsModified = false;
            }
            if (addedEntity.Properties.Any(p => p.Metadata.Name == "IsActive"))
            {
                addedEntity.Property("IsActive").CurrentValue = true;
            }

            await _context.SaveChangesAsync(ct);
        }


        public async Task DeleteAsync(object id, CancellationToken ct = default)
        {
            TEntity entity = await _context.Set<TEntity>().FindAsync(new object[] { id }, ct);
            if (entity == null) return;

            var deletedEntity = _context.Entry(entity);
            deletedEntity.State = EntityState.Modified;

            if (deletedEntity.Properties.Any(p => p.Metadata.Name == "CreationDate"))
            {
                deletedEntity.Property("CreationDate").IsModified = false;
            }
            if (deletedEntity.Properties.Any(p => p.Metadata.Name == "DeletedDate"))
            {
                deletedEntity.Property("DeletedDate").CurrentValue = DateTime.Now;
            }
            if (deletedEntity.Properties.Any(p => p.Metadata.Name == "UpdatedDate"))
            {
                deletedEntity.Property("UpdatedDate").IsModified = false;
            }
            if (deletedEntity.Properties.Any(p => p.Metadata.Name == "IsActive"))
            {
                deletedEntity.Property("IsActive").CurrentValue = false;
            }

            await _context.SaveChangesAsync(ct);
        }


        public async Task HardDeleteAsync(object id, CancellationToken ct = default)
        {
            TEntity entity = await _context.Set<TEntity>().FindAsync(new object[] { id }, ct);
            if (entity == null) return;

            var deletedEntity = _context.Entry(entity);
            deletedEntity.State = EntityState.Deleted;

            await _context.SaveChangesAsync(ct);
        }


        public async Task<TEntity> GetAsync(Expression<Func<TEntity, bool>> filter, CancellationToken ct = default)
        {
            return await _context.Set<TEntity>().AsNoTracking().SingleOrDefaultAsync(filter, ct);
        }


        public async Task<List<TEntity>> GetAllAsync(Expression<Func<TEntity, bool>> filter = null, CancellationToken ct = default)
        {
            var query = _context.Set<TEntity>().AsNoTracking();
            if (filter != null)
            {
                query = query.Where(filter);
            }
            return await query.ToListAsync(ct);
        }


        public async Task UpdateAsync(TEntity entity, CancellationToken ct = default)
        {
            var updatedEntity = _context.Entry(entity);
            updatedEntity.State = EntityState.Modified;

            if (updatedEntity.Properties.Any(p => p.Metadata.Name == "CreationDate"))
            {
                updatedEntity.Property("CreationDate").IsModified = false;
            }
            if (updatedEntity.Properties.Any(p => p.Metadata.Name == "DeletedDate"))
            {
                updatedEntity.Property("DeletedDate").IsModified = false;
            }
            if (updatedEntity.Properties.Any(p => p.Metadata.Name == "UpdatedDate"))
            {
                updatedEntity.Property("UpdatedDate").CurrentValue = DateTime.Now;
            }

            await _context.SaveChangesAsync(ct);
        }
    }
}
