using Core.DataAccess;
using Core.Utilities.Paging;
using Core.Utilities.Results;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace DataAccess.Abstract
{
    public interface ISystemExerciseDal : IEntityRepository<SystemExercise>
    {
        // Async imzalar (EF/IO yapan tüm metotlar için)
        Task<List<SystemExerciseDto>> GetAllSystemExercisesAsync(CancellationToken ct = default);
        Task<List<SystemExerciseDto>> GetSystemExercisesByCategoryAsync(int categoryId, CancellationToken ct = default);
        Task<PaginatedResult<SystemExerciseDto>> GetSystemExercisesFilteredAsync(SystemExerciseFilterDto filter, CancellationToken ct = default);
        Task<List<SystemExerciseDto>> SearchSystemExercisesAsync(string searchTerm, CancellationToken ct = default);
        Task<SystemExerciseDto> GetSystemExerciseDetailAsync(int exerciseId, CancellationToken ct = default);

        // SOLID prensiplerine uygun: Validation ve soft delete logic DAL katmanında
        Task<IResult> SoftDeleteSystemExerciseWithValidationAsync(int exerciseId, CancellationToken ct = default);

        // SOLID prensiplerine uygun: Entity oluşturma ve tarih yönetimi DAL katmanında
        Task<IResult> AddSystemExerciseWithManagementAsync(SystemExerciseAddDto exerciseAddDto, CancellationToken ct = default);
        Task<IResult> UpdateSystemExerciseWithManagementAsync(SystemExerciseUpdateDto exerciseUpdateDto, CancellationToken ct = default);
    }
}
