﻿using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using System.Threading;
using System.Threading.Tasks;

using System.Collections.Generic;

namespace Business.Concrete
{
    public class LicensePackageManager : ILicensePackageService
    {
        private readonly ILicensePackageDal _licensePackageDal;

        public LicensePackageManager(ILicensePackageDal licensePackageDal)
        {
            _licensePackageDal = licensePackageDal;
        }


        // ASYNC UYGULAMALAR
        [SecuredOperation("owner")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("LicensePackage")]
        public async Task<IResult> AddAsync(LicensePackage licensePackage, CancellationToken ct = default)
        {
            return await _licensePackageDal.AddLicensePackageAsync(licensePackage, ct);
        }

        [SecuredOperation("owner")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("LicensePackage")]
        public async Task<IResult> DeleteAsync(int id, CancellationToken ct = default)
        {
            return await _licensePackageDal.SoftDeleteLicensePackageAsync(id, ct);
        }

        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        [CacheAspect(14400)]
        public async Task<IDataResult<List<LicensePackage>>> GetAllAsync(CancellationToken ct = default)
        {
            var list = await _licensePackageDal.GetAllAsync(lp => lp.IsActive == true, ct);
            return new SuccessDataResult<List<LicensePackage>>(list);
        }

        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        [CacheAspect(14400)]
        public async Task<IDataResult<LicensePackage>> GetByIdAsync(int id, CancellationToken ct = default)
        {
            var item = await _licensePackageDal.GetAsync(lp => lp.LicensePackageID == id, ct);
            return new SuccessDataResult<LicensePackage>(item);
        }

        [SecuredOperation("owner")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("LicensePackage")]
        public async Task<IResult> UpdateAsync(LicensePackage licensePackage, CancellationToken ct = default)
        {
            return await _licensePackageDal.UpdateLicensePackageAsync(licensePackage, ct);
        }

    }
}