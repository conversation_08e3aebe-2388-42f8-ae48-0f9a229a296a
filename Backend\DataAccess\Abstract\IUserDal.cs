﻿using Core.DataAccess;
using Core.Entities.Concrete;
using Core.Utilities.Results;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace DataAccess.Abstract
{
    public interface IUserDal:IEntityRepository<User>
    {
        // SYNC <PERSON> (kaldırıldı)

        // ASYNC yü<PERSON>yler (eklenir)
        Task<List<OperationClaim>> GetClaimsAsync(User user, CancellationToken ct = default);
        Task<List<User>> GetNonMembersAsync(CancellationToken ct = default);
        Task<List<User>> GetNonMembersPaginatedAsync(int page, int pageSize, string searchTerm, CancellationToken ct = default);
        Task<int> GetNonMembersCountAsync(string searchTerm, CancellationToken ct = default);
        Task<IResult> SyncCompanyUserDataAsync(User updatedUser, User oldUser, CancellationToken ct = default);

        // SOLID prensiplerine uygun: Profile image management operations (yalnızca async)
        Task<IResult> UpdateProfileImagePathAsync(int userId, string imagePath, CancellationToken ct = default);
        Task<IResult> ClearProfileImagePathAsync(int userId, CancellationToken ct = default);

        // SOLID prensiplerine uygun: Validation logic DAL katmanında (yalnızca async)
        Task<IDataResult<User>> GetUserByIdWithValidationAsync(int userId, CancellationToken ct = default);
    }
}
