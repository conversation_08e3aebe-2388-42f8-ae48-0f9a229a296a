﻿using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Core.Aspects.Autofac.Performance;

using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Entities.Concrete;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Business.Concrete
{
    public class UserOperationClaimManager : IUserOperationClaimService
    {
        private readonly IUserOperationClaimDal _userOperationClaimDal;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public UserOperationClaimManager(
            IUserOperationClaimDal userOperationClaimDal,
            IHttpContextAccessor httpContextAccessor)
        {
            _userOperationClaimDal = userOperationClaimDal;
            _httpContextAccessor = httpContextAccessor;
        }

        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        [CacheAspect(3600)]
        public async Task<IDataResult<List<UserOperationClaimDto>>> GetUserOperationClaimDetailsAsync(CancellationToken ct = default)
        {
            var list = await _userOperationClaimDal.GetUserOperationClaimDetailsAsync(ct);
            return new SuccessDataResult<List<UserOperationClaimDto>>(list);
        }



        [SecuredOperation("owner")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("UserOperationClaim")]
        public async Task<IResult> AddAsync(UserOperationClaim userOperationClaim, CancellationToken ct = default)
        {
            await _userOperationClaimDal.AddAsync(userOperationClaim, ct);
            await InvalidateUserClaimsAsync(userOperationClaim.UserId, ct);
            return new SuccessResult(Messages.UserOperationClaimAdded);
        }


        [SecuredOperation("owner")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("UserOperationClaim")]
        public async Task<IResult> DeleteAsync(int id, CancellationToken ct = default)
        {
            var claim = await _userOperationClaimDal.GetAsync(x => x.UserOperationClaimId == id, ct);
            if (claim != null)
            {
                await _userOperationClaimDal.HardDeleteAsync(id, ct);
                await InvalidateUserClaimsAsync(claim.UserId, ct);
            }
            return new SuccessResult(Messages.UserOperationClaimDeleted);
        }


        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        [CacheAspect(3600)]
        public async Task<IDataResult<List<UserOperationClaim>>> GetAllAsync(CancellationToken ct = default)
        {
            var list = await _userOperationClaimDal.GetAllAsync(u => u.IsActive == true, ct);
            return new SuccessDataResult<List<UserOperationClaim>>(list, Messages.UserOperationClaimsListed);
        }


        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        [CacheAspect(3600)]
        public async Task<IDataResult<UserOperationClaim>> GetByIdAsync(int id, CancellationToken ct = default)
        {
            var entity = await _userOperationClaimDal.GetAsync(u => u.UserOperationClaimId == id, ct);
            return new SuccessDataResult<UserOperationClaim>(entity);
        }


        [SecuredOperation("owner")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspectAsync("UserOperationClaim")]
        public async Task<IResult> UpdateAsync(UserOperationClaim userOperationClaim, CancellationToken ct = default)
        {
            await _userOperationClaimDal.UpdateAsync(userOperationClaim, ct);
            await InvalidateUserClaimsAsync(userOperationClaim.UserId, ct);
            return new SuccessResult(Messages.UserOperationClaimUpdated);
        }


        public async Task<IResult> InvalidateUserClaimsAsync(int userId, CancellationToken ct = default)
        {
            return await _userOperationClaimDal.InvalidateUserTokensByUserIdAsync(userId, ct);
        }


        [LogAspect]
        public async Task<IResult> AddForRegistrationAsync(UserOperationClaim userOperationClaim, CancellationToken ct = default)
        {
            var ipAddress = _httpContextAccessor.HttpContext?.Connection?.RemoteIpAddress?.ToString() ?? "Unknown";
            var timestamp = DateTime.Now;
            Console.WriteLine($"[{timestamp}] AddForRegistrationAsync called: UserId={userOperationClaim.UserId}, RoleId={userOperationClaim.OperationClaimId}, IP: {ipAddress}");
            await _userOperationClaimDal.AddAsync(userOperationClaim, ct);
            return new SuccessResult(Messages.UserOperationClaimAdded);
        }
    }
}
