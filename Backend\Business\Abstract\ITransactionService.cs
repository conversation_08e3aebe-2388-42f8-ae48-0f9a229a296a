﻿using Core.Utilities.Results;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace Business.Abstract
{
    public interface ITransactionService
    {
        // Yalnızca ASYNC yüzey
        Task<IDataResult<List<Transaction>>> GetAllAsync(CancellationToken ct = default);
        Task<IDataResult<List<Transaction>>> GetByMemberIdAsync(int memberId, CancellationToken ct = default);
        Task<IResult> AddAsync(Transaction transaction, CancellationToken ct = default);
        Task<IDataResult<List<TransactionDetailDto>>> GetTransactionsWithDetailsAsync(CancellationToken ct = default);
        Task<IResult> UpdatePaymentStatusAsync(int transactionId, CancellationToken ct = default);
        Task<IDataResult<List<TransactionDetailDto>>> GetUnpaidTransactionsAsync(int memberId, CancellationToken ct = default);
        Task<IResult> AddBulkAsync(BulkTransactionDto bulkTransaction, CancellationToken ct = default);
        Task<IResult> UpdateAllPaymentStatusAsync(int memberId, CancellationToken ct = default);
        Task<IResult> DeleteAsync(int transactionId, CancellationToken ct = default);
        Task<IDataResult<decimal>> GetMonthlyTransactionTotalAsync(int year, int month, CancellationToken ct = default);
        Task<IDataResult<decimal>> GetDailyTransactionTotalAsync(DateTime date, CancellationToken ct = default);
    }
}
