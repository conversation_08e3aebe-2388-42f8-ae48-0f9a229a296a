using Core.Utilities.Results;
using Entities.DTOs;
using System.Threading;
using System.Threading.Tasks;

namespace Business.Abstract
{
    public interface ICacheAdminService
    {
        // Tenant (current company)
        Task<IDataResult<object>> GetCacheStatisticsAsync(CancellationToken ct = default);
        Task<IDataResult<object>> GetCacheHealthAsync(CancellationToken ct = default);
        Task<IDataResult<object>> GetCompanyCacheKeysAsync(int page = 1, int size = 50, CancellationToken ct = default);
        Task<IDataResult<object>> GetKeysByPatternAsync(string pattern, int page = 1, int size = 50, CancellationToken ct = default);
        Task<IDataResult<object>> ClearTenantCacheAsync(CancellationToken ct = default);
        Task<IDataResult<object>> ClearCacheByPatternAsync(string pattern, CancellationToken ct = default);
        Task<IDataResult<object>> GetTenantCacheDetailsAsync(CancellationToken ct = default);
        Task<IDataResult<object>> WarmupCacheAsync(CacheWarmupRequestDto request, CancellationToken ct = default);
        Task<IDataResult<object>> GetRealtimeMetricsAsync(CancellationToken ct = default);
        Task<IDataResult<object>> DeleteCacheKeyAsync(string key, CancellationToken ct = default);
        Task<IDataResult<object>> GetCacheKeyValueAsync(string key, CancellationToken ct = default);

        // Multi-company (admin screens)
        Task<IDataResult<object>> GetAllCompaniesStatisticsAsync(CancellationToken ct = default);
        Task<IDataResult<object>> GetSpecificCompanyCacheDetailsAsync(int companyId, CancellationToken ct = default);
        Task<IDataResult<object>> ClearSpecificCompanyCacheAsync(int companyId, CancellationToken ct = default);
        Task<IDataResult<object>> BulkClearCompaniesCacheAsync(BulkCacheOperationRequestDto request, CancellationToken ct = default);
    }
}

